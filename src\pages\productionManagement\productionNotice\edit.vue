<script setup lang="ts" name="ProductionNoticeEdit">
import { ElMessage } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import currency from 'currency.js'
import AddBaseInfoForm from './components/AddBaseInfoForm.vue'
import AddGreyInfoCard from './components/AddGreyInfoCard.vue'
import AddUseMaterScaleCard from './components/AddUseMaterScaleCard.vue'
import ProcessRequirementCard from './components/ProcessRequirementCard.vue'
import {
  editfProductionNotify,
  getByIdProcessRequirement,
  getProductionNotifyOrder,
} from '@/api/productionNotice'
import {
  formatDate,
  formatTwoDecimalsDiv,
} from '@/common/format'
import { PHONE_REGEXP } from '@/common/rule'
import useRouterList from '@/use/useRouterList'
import { processDataIn, processDataOut } from '@/common/handBinary'
import { GetGlobalConfig } from '@/api/common'
import { GlobalEnum } from '@/common/enum'
import { debounce } from '@/common/util'

const routerList = useRouterList()
const route = useRoute()
const state = reactive<any>({
  form: {
    weaving_specifications_id: [],
    needle_size: '',
    total_needle_size: '',
    weaving_loss: '',
    loom_brand: '',
    loom_model_id: '',
    machines_num: '',
    upper_needle: '',
    yarn_batch: '',
    lower_needle: '',
    yarn_arrange: '',
    yarn_length: '',
    packaging_require: '',
  },
  schedulingWeight: 0,
  baseData: {},
})

const AddBaseInfoFormRef = ref()
const addGreyInfoCard = ref()
const AddUseMaterScaleCardRef = ref()

interface customStateType {
  warp_density: string | number
  weft_density: string | number
  reed_inner_width: string | number
  reed_outer_width: string | number
  reed_no: string | number
  penetration_number: string | number
  upper_weft_density: string | number
  lower_weft_density: string | number
  gf_theory_gram_width: string | number
  total_warp_pieces: string | number
  tableData: any[]
  warp_arrangement: string | number
  tableData1: any[]
  weft_arrangement: string | number
  handleDel: (type: number, rowIndex: number) => void
}

const customState = reactive<customStateType>({
  warp_density: '',
  weft_density: '',
  reed_inner_width: '',
  reed_outer_width: '',
  reed_no: '',
  penetration_number: '',
  upper_weft_density: '',
  lower_weft_density: '',
  gf_theory_gram_width: '',
  total_warp_pieces: '',
  tableData: [], // 经纱
  warp_arrangement: '',
  tableData1: [], // 纬纱
  weft_arrangement: '',
  handleDel: (type: number, rowIndex: number) => {
    if (type === 1) {
      customState.tableData = customState.tableData.filter(
        (item: any, index: number) => rowIndex !== index,
      )
    }
    else if (type === 2) {
      customState.tableData1 = customState.tableData1.filter(
        (item: any, index: number) => index !== rowIndex,
      )
    }
  },
})

// 工艺要求数据
const processRequirementData = ref({
  weavingForm: {
    weaving_specifications_id: [],
    weaving_specifications_name_list: '',
    needle_size: '',
    total_needle_size: '',
    weaving_loss: '',
    loom_brand: '',
    loom_model_id: '',
    loom_model_name: '',
    machines_num: '',
    upper_needle: '',
    yarn_batch: '',
    lower_needle: '',
    yarn_arrange: '',
    yarn_length: '',
    packaging_require: '',
  },
  shuttleWeavingData: {
    warp_density: '',
    weft_density: '',
    reed_inner_width: '',
    reed_outer_width: '',
    reed_no: '',
    penetration_number: '',
    upper_weft_density: '',
    lower_weft_density: '',
    gf_theory_gram_width: '',
    total_warp_pieces: '',
    tableData: [],
    warp_arrangement: '',
    tableData1: [],
    weft_arrangement: '',
  },
  fabricFlyData: {
    tableData1: [],
    tableData2: [],
  },
})

const processRequirementCardRef = ref()

const detailLoading = ref(false)
const isSync = ref(false)
// 获取月结日
const { fetchData: fetchGlobalConfig, data: globalConfigData } = GetGlobalConfig()
const getGlobalConfig = debounce(async () => {
  await fetchGlobalConfig({
    id: GlobalEnum.ProductionNoticeCustomerSync,
  })
  isSync.value = globalConfigData.value.options === '同步'
}, 500)
const {
  fetchData: detailFetch,
  data: detailData,
  success: detailSuccess,
  msg: detailMsg,
} = getProductionNotifyOrder()

onMounted(() => {
  getData()
  getGlobalConfig()
})

// watch(detailData, (val) => {
//

// })

async function getData() {
  detailLoading.value = true
  await detailFetch({ id: route.query.id })

  detailData.value = processDataOut(detailData.value)

  if (detailSuccess.value) {
    // 基础信息
    AddBaseInfoFormRef.value.setData(detailData.value)
    // 坯布信息
    addGreyInfoCard.value.setData(detailData.value)
    // 用料比例
    AddUseMaterScaleCardRef.value.setData(detailData.value)
    // 工艺要求 - 设置 ProcessRequirementCard 组件的数据
    processRequirementData.value = {
      weavingForm: {
        weaving_specifications_id: detailData.value.weaving_specifications?.map(
          (item: any) => item.weaving_specifications_id,
        ) || [],
        weaving_specifications_name_list: detailData.value.weaving_specifications
          ?.map((v: any) => v.weaving_specifications_name)
          .join(',') || '',
        needle_size: detailData.value.needle_size || '',
        total_needle_size: detailData.value.total_needle_size || '',
        weaving_loss: detailData.value.weaving_loss || '',
        loom_brand: detailData.value.loom_brand || '',
        loom_model_id: detailData.value.loom_model_id || '',
        loom_model_name: detailData.value.loom_model_name || '',
        machines_num: detailData.value.machines_num || '',
        upper_needle: detailData.value.upper_needle || '',
        yarn_batch: detailData.value.yarn_batch || '',
        lower_needle: detailData.value.lower_needle || '',
        yarn_arrange: detailData.value.yarn_arrange || '',
        yarn_length: detailData.value.yarn_length || '',
        packaging_require: detailData.value.packaging_require || '',
      },
      shuttleWeavingData: {
        warp_density: detailData.value.warp_density || '',
        weft_density: detailData.value.weft_density || '',
        reed_inner_width: detailData.value.reed_inner_width || '',
        reed_outer_width: detailData.value.reed_outer_width || '',
        reed_no: detailData.value.reed_no || '',
        penetration_number: detailData.value.penetration_number || '',
        upper_weft_density: detailData.value.upper_weft_density || '',
        lower_weft_density: detailData.value.lower_weft_density || '',
        gf_theory_gram_width: detailData.value.gf_theory_gram_width || '',
        total_warp_pieces: detailData.value.total_warp_pieces || '',
        warp_arrangement: detailData.value.warp_arrangement || '',
        weft_arrangement: detailData.value.weft_arrangement || '',
        tableData: detailData.value.warp_datas?.map((item: any) => {
          return {
            ...item,
            total_use_weight: item.total_use_weight,
            net_use_weight: item.net_use_weight,
          }
        }) || [],
        tableData1: detailData.value.weft_datas?.map((item: any) => {
          return {
            ...item,
            total_use_weight: item.total_use_weight,
            net_use_weight: item.net_use_weight,
          }
        }) || [],
      },
      fabricFlyData: {
        tableData1: [],
        tableData2: [],
      },
    }

    // 同步到原有的 state.form 和 customState（保持兼容性）
    const keys = [
      'weaving_specifications',
      'needle_size',
      'total_needle_size',
      'weaving_loss',
      'loom_brand',
      'loom_model_id',
      'machines_num',
      'upper_needle',
      'yarn_batch',
      'lower_needle',
      'yarn_arrange',
      'yarn_length',
      'packaging_require',
      'loom_model_name',
    ]
    keys.forEach((key: string) => {
      if (detailData.value[key]) {
        if (key === 'weaving_specifications') {
          state.form.weaving_specifications_id
            = detailData.value.weaving_specifications?.map(
              (item: any) => item.weaving_specifications_id,
            )
        }
        else {
          state.form[key] = detailData.value[key]
        }
      }
    })

    customState.warp_density = detailData.value.warp_density
    customState.weft_density = detailData.value.weft_density
    customState.reed_inner_width = detailData.value.reed_inner_width
    customState.reed_outer_width = detailData.value.reed_outer_width
    customState.reed_no = detailData.value.reed_no
    customState.penetration_number = detailData.value.penetration_number
    customState.upper_weft_density = detailData.value.upper_weft_density
    customState.lower_weft_density = detailData.value.lower_weft_density
    customState.gf_theory_gram_width = detailData.value.gf_theory_gram_width
    customState.total_warp_pieces = detailData.value.total_warp_pieces
    customState.warp_arrangement = detailData.value.warp_arrangement
    customState.weft_arrangement = detailData.value.weft_arrangement

    customState.tableData = detailData.value.warp_datas?.map((item: any) => {
      return {
        ...item,
        total_use_weight: item.total_use_weight,
        net_use_weight: item.net_use_weight,
      }
    })
    customState.tableData1 = detailData.value.weft_datas?.map((item: any) => {
      return {
        ...item,
        total_use_weight: item.total_use_weight,
        net_use_weight: item.net_use_weight,
      }
    })
  }
  else {
    ElMessage.error(detailMsg.value)
  }
}
function closeLoading() {
  detailLoading.value = false
}

function getGreyInfoList(list: any, { sale_system_id, production_plan_order_id }: any) {
  addGreyInfoCard.value.setList(list, sale_system_id, production_plan_order_id)
}
// item营销体系 id生产计划单
// const selectSaleSystemId = (item: any, id: string | number | null | undefined) => {
//   addGreyInfoCard.value.selectSaleSystemId(item, id)
// }
// 拿到排产数量
function setSchedulingWeight(schedulingWeight: number) {
  state.schedulingWeight = schedulingWeight
}
function setOrderNo(order_no: any) {
  addGreyInfoCard.value.setProductionOrderNo(order_no)
}

function setGreyId(id: any) {
  setProcessRequirementData(id)
  AddUseMaterScaleCardRef.value.setGreyId(id)
}

// 处理工艺要求数据更新
function handleProcessRequirementUpdate(data: any) {
  processRequirementData.value = data
  // 同步到原有的 state.form 和 customState
  Object.assign(state.form, data.weavingForm)
  Object.assign(customState, data.shuttleWeavingData)
}

// 处理织机机型选择
function handleLoomModelSelect(item: any) {
  state.form.loom_model_name = item.name
}

// item营销体系 id生产计划单
function selectSaleSystemId(item: any, id: string | number | null | undefined) {
  addGreyInfoCard.value.selectSaleSystemId(item, id)
  // state.sale_system_id = item.id
}
function selectCustomer(item: any) {
  if (isSync.value)
    addGreyInfoCard.value.selectCustomerId(item)
}
// 获取工艺要求
const {
  fetchData: ApiProcessRequirementList,
  data: processRequirementdatalist,
  success: ProcessRequirementSuccess,
  msg: ProcessRequirementMsg,
} = getByIdProcessRequirement()
async function setProcessRequirementData(id: any) {
  await ApiProcessRequirementList({ id })
  if (ProcessRequirementSuccess.value) {
    const data = processRequirementdatalist.value

    // 更新 processRequirementData
    processRequirementData.value = {
      weavingForm: {
        weaving_specifications_id: data.weaving_specifications?.map(
          (item: any) => item.weaving_specifications_id,
        ) || [],
        weaving_specifications_name_list: data.weaving_specifications
          ?.map((v: any) => v.weaving_specifications_name)
          .join(',') || '',
        needle_size: data.needle_size || '',
        total_needle_size: data.total_needle_size || '',
        weaving_loss: data.weaving_loss || '',
        loom_brand: data.loom_brand || '',
        loom_model_id: data.loom_model_id || '',
        loom_model_name: data.loom_model_name || '',
        machines_num: data.machines_num || '',
        upper_needle: data.upper_needle || '',
        yarn_batch: data.yarn_batch || '',
        lower_needle: data.lower_needle || '',
        yarn_arrange: data.yarn_arrange || '',
        yarn_length: data.yarn_length || '',
        packaging_require: data.packaging_require || '',
      },
      shuttleWeavingData: {
        warp_density: data.warp_density || '',
        weft_density: data.weft_density || '',
        reed_inner_width: data.reed_inner_width || '',
        reed_outer_width: data.reed_outer_width || '',
        reed_no: data.reed_no || '',
        penetration_number: data.penetration_number || '',
        upper_weft_density: data.upper_weft_density || '',
        lower_weft_density: data.lower_weft_density || '',
        gf_theory_gram_width: data.gf_theory_gram_width || '',
        total_warp_pieces: data.total_warp_pieces || '',
        warp_arrangement: data.warp_arrangement || '',
        weft_arrangement: data.weft_arrangement || '',
        tableData: data.warp_datas || [],
        tableData1: data.weft_datas || [],
      },
      fabricFlyData: {
        tableData1: [],
        tableData2: [],
      },
    }

    // 保持原有逻辑的兼容性
    const arr = [
      'needle_size',
      'total_needle_size',
      'upper_needle',
      'lower_needle',
      'weaving_loss',
      'yarn_batch',
      'yarn_length',
      'yarn_arrange',
    ]
    arr.forEach((key: string) => {
      state.form[key] = data[key]
    })
    state.form.weaving_loss = formatTwoDecimalsDiv(data.weaving_loss)

    // 匹配坯布完整信息--带出单位
    addGreyInfoCard.value.setLineData(data)
  }
  else {
    ElMessage.error(ProcessRequirementMsg.value)
  }
}

function getFormData(form: any) {
  state.baseData = {
    ...form,
  }
}
// 提交所有数据
const {
  fetchData: ApiNotifyList,
  data: successData,
  success: notifySuccess,
  msg: notifyMsg,
} = editfProductionNotify()
async function submitData() {
  await AddBaseInfoFormRef.value.submitData()
  const greyInfo = addGreyInfoCard.value.handSubmit()

  const materList = AddUseMaterScaleCardRef.value.submitData()
  if (!state.baseData.sale_system_id)
    return ElMessage.error('营销体系为空')

  if (!state.baseData.weaving_mill_id)
    return ElMessage.error('织厂名称为空')

  if (!state.baseData.customer_id)
    return ElMessage.error('客户名称为空')

  if (!state.baseData.sale_user_id)
    return ElMessage.error('销售员为空')

  // 坯布信息校验 start
  let valid = true
  const greyData = [greyInfo].filter((v: any) => v)
  greyData.forEach((item: any) => {
    if (!item.customer_id)
      valid = false
  })
  if (!valid)
    return ElMessage.error('请选择所属客户')
  if (!greyData.length)
    return ElMessage.error('请添加坯布信息')

  const all_scheduling_roll
    = greyData[0].production_notify_grey_fabric_detail?.reduce(
      (pre: any, val: any) => pre + Number(val.this_scheduling_roll),
      0,
    ) || 0

  if (Number(greyData[0].scheduling_roll) < all_scheduling_roll)
    return ElMessage.error('排产匹数已超过关联的销售计划单可排产匹数之和')

  // 坯布信息校验 end

  // 原料信息校验 start
  let allRate = 0
  for (let i = 0; i < materList.length; i++) {
    if (!Number(materList[i].yarn_loss))
      return ElMessage.error('用纱损耗不能空')

    if (!Number(materList[i].yarn_ratio))
      return ElMessage.error('用纱比例不能空')

    allRate = currency(materList[i].yarn_ratio).add(allRate).value
  }
  if (materList.length && allRate !== 100)
    return ElMessage.error('用纱比例总和需为100%')

  // 原料信息校验 end

  // 从 ProcessRequirementCard 组件获取最新数据
  const processRequirementCurrentData = processRequirementCardRef.value?.getData() || processRequirementData.value

  const query = processDataIn({
    ...greyInfo, // 坯布信息
    production_notify_material_ratio: materList || [], // 用料信息
    ...state.baseData,
    settle_type_id: state.baseData.settle_type_id,
    production_notify_grey_fabric_detail:
      greyInfo.production_notify_grey_fabric_detail?.map((item: any) => {
        return {
          ...item,
          create_time: formatDate(item.create_time),
          order_time: formatDate(item.order_time),
        }
      }) || [],

    // 从 ProcessRequirementCard 组件获取的织造工艺数据
    ...processRequirementCurrentData.weavingForm,
    // 从 ProcessRequirementCard 组件获取的梭织工艺数据
    ...processRequirementCurrentData.shuttleWeavingData,

    // 织造规格ID
    weaving_specifications_ids: processRequirementCurrentData.weavingForm.weaving_specifications_id?.join(',') || '',
    warp_datas: processRequirementCurrentData.shuttleWeavingData.tableData?.map((item: any) => {
      return {
        ...item,
        material_type: 1,
      }
    }) || [],
    weft_datas: processRequirementCurrentData.shuttleWeavingData.tableData1?.map((item: any) => {
      return {
        ...item,
        material_type: 2,
      }
    }) || [],
  })

  query.weaving_mill_id = Number(state.baseData.weaving_mill_id)
  // 再次校验基础信息
  if (
    !(
      query.notify_date
      && query.sale_system_id
      && query.weaving_mill_id
      && query.customer_id
    )
  )
    return ElMessage.error('请补充基础信息')

  if (
    query.weaving_mill_order_follower_phone
    && !PHONE_REGEXP.test(query.weaving_mill_order_follower_phone)
  )
    return ElMessage.error('基础信息-请输入正确的跟单电话')

  await ApiNotifyList({
    ...query,
    id: detailData.value.id,
  })
  if (notifySuccess.value) {
    ElMessage.success('提交成功')
    await getData()
    routerList.push({
      name: 'ProductionNoticeDetail',
      query: {
        id: successData.value.id,
      },
    })
  }
  else {
    ElMessage.error(notifyMsg.value)
  }
}
function setForm(form: any) {
  AddBaseInfoFormRef.value.setForm(form)
}
</script>

<template>
  <AddBaseInfoForm
    ref="AddBaseInfoFormRef"
    type="edit"
    @set-order-no="setOrderNo"
    @set-grey-id="setGreyId"
    @get-form-data="getFormData"
    @get-grey-info-list="getGreyInfoList"
    @select-sale-system-id="selectSaleSystemId"
    @select-customer="selectCustomer"
    @submit-data="submitData"
  />
  <AddGreyInfoCard
    ref="addGreyInfoCard"
    :is-sync="isSync"
    :detail-loading="detailLoading"
    @set-form="setForm"
    @close-loading="closeLoading"
    @set-grey-id="setGreyId"
    @set-scheduling-weight="setSchedulingWeight"
  />
  <AddUseMaterScaleCard
    ref="AddUseMaterScaleCardRef"
    :scheduling-weight="state.schedulingWeight"
  />
  <!-- 工艺要求 -->
  <ProcessRequirementCard
    ref="processRequirementCardRef"
    v-model="processRequirementData"
    mode="edit"
    :production-notify-order-id="Number(route.query.id)"
    @update:model-value="handleProcessRequirementUpdate"
    @loom-model-select="handleLoomModelSelect"
  />
</template>

<style lang="scss" scoped>
::v-deep .el-form-item {
  width: 100%;
}

.button_text_content {
  display: flex;
  margin-top: 4px;

  .custom_button {
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    font-size: 26px;
    cursor: pointer;
    border-radius: 2px;
    user-select: none;

    &:hover {
      background: rgba(14, 126, 255, 0.1);
    }
  }
}

.flex_center {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
}
</style>
