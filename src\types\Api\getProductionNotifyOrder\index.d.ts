declare namespace Api.GetProductionNotifyOrder {
  export interface Request {
    /**
     * id
     */
    id: number
    [property: string]: any
  }
  /**
   * produce.GetProductionNotifyOrderDetailData
   */
  export interface Response {
  /**
   * 审核时间
   */
    audit_date?: string
    /**
     * 审核人ID
     */
    auditor_id?: number
    /**
     * 审核人名称
     */
    auditor_name?: string
    /**
     * 业务关闭 1.正常 2。关闭
     */
    business_close?: number
    /**
     * 业务关闭名称
     */
    business_close_name?: string
    /**
     * 业务关闭时间
     */
    business_close_time?: string
    /**
     * 业务关闭人
     */
    business_close_user_id?: number
    /**
     * 业务关闭人名称
     */
    business_close_user_name?: string
    /**
     * 变更条数
     */
    change_roll?: number
    /**
     * 变更数量
     */
    change_weight?: number
    /**
     * 公司ID
     */
    company_id?: number
    /**
     * 创建时间
     */
    create_time?: string
    /**
     * 创建人
     */
    creator_id?: number
    /**
     * 创建人
     */
    creator_name?: string
    /**
     * 传真
     */
    customer_fax_number?: string
    /**
     * 客户ID
     */
    customer_id?: number
    /**
     * 客户名称
     */
    customer_name?: string
    /**
     * 客户跟单用户ID
     */
    customer_order_follower_id?: number
    /**
     * 客户跟单用户名称
     */
    customer_order_follower_name?: string
    /**
     * 删除备注
     */
    delete_remark?: string
    /**
     * 送货地址
     */
    delivery_addr?: string
    /**
     * 下单用户所属部门
     */
    department_id?: number
    /**
     * 成品克重(2)
     */
    finish_product_gram_weight?: string
    /**
     * 成品克重及单位名称
     */
    finish_product_gram_weight_and_unit_name?: string
    /**
     * 成品克重单位id(字典)
     */
    finish_product_gram_weight_unit_id?: number
    /**
     * 成品克重单位名称
     */
    finish_product_gram_weight_unit_name?: string
    /**
     * 成品幅宽(2)
     */
    finish_product_width?: string
    /**
     * 成品幅宽及单位名称
     */
    finish_product_width_and_unit_name?: string
    /**
     * 成品幅宽单位id(字典)
     */
    finish_product_width_unit_id?: number
    /**
     * 成品幅宽单位名称
     */
    finish_product_width_unit_name?: string
    /**
     * 理论坯布克重
     */
    gf_theory_gram_width?: number
    /**
     * 坯布编号
     */
    grey_fabric_code?: string
    /**
     * 坯布颜色id
     */
    grey_fabric_color_id?: number
    /**
     * 坯布颜色Name
     */
    grey_fabric_color_name?: string
    /**
     * 坯布成分
     */
    grey_fabric_composition?: string
    /**
     * 坯布克重
     */
    grey_fabric_gram_weight?: string
    /**
     * 坯布克重及单位名称
     */
    grey_fabric_gram_weight_and_unit_name?: string
    /**
     * 坯布克重单位id(字典)
     */
    grey_fabric_gram_weight_unit_id?: number
    /**
     * 坯布克重单位名称
     */
    grey_fabric_gram_weight_unit_name?: string
    /**
     * 坯布信息ID
     */
    grey_fabric_id?: number
    /**
     * 坯布名称
     */
    grey_fabric_name?: string
    /**
     * 坯布信息备注
     */
    grey_fabric_remark?: string
    /**
     * 坯布幅宽
     */
    grey_fabric_width?: string
    /**
     * 坯布幅宽及单位名称
     */
    grey_fabric_width_and_unit_name?: string
    /**
     * 坯布幅宽单位id(字典)
     */
    grey_fabric_width_unit_id?: number
    /**
     * 坯布幅宽单位名称
     */
    grey_fabric_width_unit_name?: string
    /**
     * 记录ID
     */
    id?: number
    /**
     * 发票抬头
     */
    invoice_header?: number
    /**
     * 发票抬头
     */
    invoice_header_name?: string
    /**
     * 是否是系统自动分配
     */
    is_auto?: boolean
    /**
     * 织机品牌
     */
    loom_brand?: string
    /**
     * 织机机型ID
     */
    loom_model_id?: number
    /**
     * 织机机型名称
     */
    loom_model_name?: string
    /**
     * 下针
     */
    lower_needle?: string
    /**
     * 下机纬密
     */
    lower_weft_density?: number
    /**
     * 安排机台数
     */
    machines_num?: number
    /**
     * 针寸数
     */
    needle_size?: string
    /**
     * 通知日期
     */
    notify_date?: string
    /**
     * 生产通知单号
     */
    order_no?: string
    /**
     * 跟单QC员
     */
    order_qc_user_id?: number
    /**
     * 跟单QC员名称
     */
    order_qc_user_name?: string
    /**
     * 单据备注
     */
    order_remark?: string
    /**
     * 其他数量
     */
    other_weight?: number
    /**
     * 包装要求
     */
    packaging_require?: string
    /**
     * 付款期限
     */
    payment_term?: number
    /**
     * 付款期限名称
     */
    payment_term_name?: string
    /**
     * 穿入数
     */
    penetration_number?: number
    /**
     * 加工单价
     */
    process_price?: number
    /**
     * 生产要求 todo: 待说明是什么
     */
    produce_require?: string
    /**
     * 已产条数
     */
    produced_roll?: number
    /**
     * 已产数量
     */
    produced_weight?: number
    /**
     * 未产条数(原排产条数+变更条数-已产匹数(收货匹数))
     */
    producing_roll?: number
    /**
     * 未产数量(原排产数量+变更数量-已产数量(收货数量))
     */
    producing_weight?: number
    /**
     * 成品克重(1),优先使用
     */
    product_gram_weight?: string
    /**
     * 成品幅宽(1),优先使用
     */
    product_width?: string
    /**
     * 坯布信息
     */
    production_notify_grey_fabric_detail?: ProduceGetProductionNotifyGreyFabricDetailData[]
    /**
     * 原料信息
     */
    production_notify_material_ratio?: ProduceGetProductionNotifyMaterialRatioData[]
    /**
     * 生产计划单ID
     */
    production_plan_order_id?: number
    /**
     * 生产计划单单号
     */
    production_plan_order_no?: string
    /**
     * 收坯地址
     */
    receipt_grey_fabric_address?: string
    /**
     * 交坯日期
     */
    receipt_grey_fabric_date?: string
    /**
     * 钢筘内幅
     */
    reed_inner_width?: number
    /**
     * 筘号
     */
    reed_no?: number
    /**
     * 钢筘外幅
     */
    reed_outer_width?: number
    /**
     * 钢筘总幅
     */
    reed_total_width?: number
    /**
     * 总条数
     */
    roll?: number
    /**
     * 营销体系地址
     */
    sale_system_addr?: string
    /**
     * 营销体系传真
     */
    sale_system_fax_number?: string
    /**
     * 营销体系ID
     */
    sale_system_id?: number
    /**
     * 营销体系名称
     */
    sale_system_name?: string
    /**
     * 营销体系经办人
     */
    sale_system_operator_name?: string
    /**
     * 营销体系电话
     */
    sale_system_phone?: string
    /**
     * 销售员
     */
    sale_user_id?: number
    /**
     * 销售员名称
     */
    sale_user_name?: string
    /**
     * 排产条数
     */
    scheduling_roll?: number
    /**
     * 排产数量
     */
    scheduling_weight?: number
    /**
     * 结算方式id
     */
    settle_type_id?: number
    /**
     * 结算方式名
     */
    settle_type_name?: string
    /**
     * 订单状态 1待审核 2已审核 3已驳回 4已作废
     */
    status?: number
    /**
     * 订单状态名称
     */
    status_name?: string
    /**
     * 总针数
     */
    total_needle_size?: string
    /**
     * 最终排产匹数
     */
    total_scheduling_roll?: number
    /**
     * 最终排产数量
     */
    total_scheduling_weight?: number
    /**
     * 总经根数
     */
    total_warp_pieces?: number
    /**
     * 单位id
     */
    unit_id?: number
    /**
     * 单位名称
     */
    unit_name?: string
    /**
     * 修改时间
     */
    update_time?: string
    /**
     * 修改人
     */
    update_user_name?: string
    /**
     * 修改人
     */
    updater_id?: number
    /**
     * 上针
     */
    upper_needle?: string
    /**
     * 上机纬密
     */
    upper_weft_density?: number
    /**
     * 经纱排列
     */
    warp_arrangement?: string
    /**
     * 坯布资料-经纱组合
     */
    warp_datas?: ProduceGetProductionNotifyOrderGreyFabricWarpWeftData[]
    /**
     * 经密
     */
    warp_density?: number
    /**
     * 织造损耗
     */
    weaving_loss?: number
    /**
     * 织厂地址
     */
    weaving_mill_addr?: string
    /**
     * 织厂传真
     */
    weaving_mill_fax_number?: string
    /**
     * 织厂ID
     */
    weaving_mill_id?: number
    /**
     * 织厂名称
     */
    weaving_mill_name?: string
    /**
     * 织厂联系人名称
     */
    weaving_mill_order_contact_name?: string
    /**
     * 织厂跟单用户ID
     */
    weaving_mill_order_follower_id?: number
    /**
     * 织厂跟单用户名称
     */
    weaving_mill_order_follower_name?: string
    /**
     * 织厂跟单电话
     */
    weaving_mill_order_follower_phone?: string
    /**
     * 织造组织id
     */
    weaving_organization_id?: number
    /**
     * 织造组织名
     */
    weaving_organization_name?: string
    /**
     * 织造规格名称
     */
    weaving_specifications?: ProduceWeavingSpecifications[]
    /**
     * 纬纱排列
     */
    weft_arrangement?: string
    /**
     * 坯布资料-纬纱组合
     */
    weft_datas?: ProduceGetProductionNotifyOrderGreyFabricWarpWeftData[]
    /**
     * 纬密
     */
    weft_density?: number
    /**
     * 布匹定重
     */
    weight_of_fabric?: number
    /**
     * 排纱
     */
    yarn_arrange?: string
    /**
     * 纱批
     */
    yarn_batch?: string
    /**
     * 纱长
     */
    yarn_length?: string
    [property: string]: any
  }

  /**
   * produce.GetProductionNotifyGreyFabricDetailData
   */
  export interface ProduceGetProductionNotifyGreyFabricDetailData {
  /**
   * 可排产匹数(可排产匹数=匹数-排产匹数-调库存匹数)
   */
    can_scheduling_roll?: number
    /**
     * 创建日期
     */
    create_time?: string
    id?: number
    /**
     * 订单日期
     */
    order_time?: string
    /**
     * 已计划匹数
     */
    planed_roll?: number
    /**
     * 已计划数量
     */
    planed_weight?: number
    /**
     * 已产匹数
     */
    produced_roll?: number
    /**
     * 已产数量
     */
    produced_weight?: number
    /**
     * 生产通知单ID
     */
    production_notify_order_id?: number
    /**
     * 匹数 100
     */
    roll?: number
    /**
     * 销售计划单坯布信息ID
     */
    sale_plan_order_gf_detail_id?: number
    /**
     * 销售计划单ID
     */
    sale_plan_order_id?: number
    /**
     * 成品销售计划单子项信息id
     */
    sale_plan_order_item_id?: number
    /**
     * 成品销售计划单子项单号
     */
    sale_plan_order_item_no?: string
    /**
     * 销售计划单单号
     */
    sale_plan_order_no?: string
    /**
     * 排产匹数
     */
    scheduling_roll?: number
    /**
     * 排产数量
     */
    scheduling_weight?: number
    /**
     * 本次排产匹数
     */
    this_scheduling_roll?: number
    /**
     * 本次排产数量
     */
    this_scheduling_weight?: number
    /**
     * 调库存匹数
     */
    use_stock_roll?: number
    /**
     * 数量 10000
     */
    weight?: number
    [property: string]: any
  }

  /**
   * produce.GetProductionNotifyMaterialRatioData
   */
  export interface ProduceGetProductionNotifyMaterialRatioData {
  /**
   * 变更发纱量
   */
    change_send_yarn_quantity?: number
    /**
     * 变更用纱量
     */
    change_use_yarn_quantity?: number
    /**
     * 原料颜色
     */
    color_scheme?: string
    /**
     * 最终发纱量
     */
    final_send_yarn_quantity?: number
    /**
     * 最终用纱量
     */
    final_use_yarn_quantity?: number
    /**
     * 坯布颜色id
     */
    grey_fabric_color_id?: number
    /**
     * 坯布颜色名称
     */
    grey_fabric_color_name?: string
    id?: number
    /**
     * 织厂出料
     */
    mill_private_yarn?: boolean
    /**
     * 生产通知单ID
     */
    production_notify_order_id?: number
    /**
     * 原料批号
     */
    raw_material_batch_number?: string
    /**
     * 原料品牌
     */
    raw_material_brand?: string
    /**
     * 原料编号
     */
    raw_material_code?: string
    /**
     * 原料ID
     */
    raw_material_id?: number
    /**
     * 原料名称
     */
    raw_material_name?: string
    /**
     * 原料库存ID
     */
    raw_stock_id?: number
    /**
     * 备注
     */
    remark?: string
    /**
     * 发纱量
     */
    send_yarn_quantity?: number
    /**
     * 供方
     */
    supplier_id?: number
    /**
     * 供方名称
     */
    supplier_name?: string
    /**
     * 单位id
     */
    unit_id?: number
    /**
     * 单位名称
     */
    unit_name?: string
    /**
     * 用纱量
     */
    use_yarn_quantity?: number
    /**
     * 织造类别
     */
    weaving_category?: string
    /**
     * 用纱损耗
     */
    yarn_loss?: number
    /**
     * 用纱比例
     */
    yarn_ratio?: number
    [property: string]: any
  }

  /**
   * produce.GetProductionNotifyOrderGreyFabricWarpWeftData
   */
  export interface ProduceGetProductionNotifyOrderGreyFabricWarpWeftData {
  /**
   * 创建时间
   */
    create_time?: string
    /**
     * 创建人
     */
    creator_id?: number
    /**
     * 创建人
     */
    creator_name?: string
    /**
     * 记录ID
     */
    id?: number
    /**
     * 数据类型：1经纱组合；2纬纱组合（前端不用穿）
     */
    material_type?: number
    /**
     * 净用量
     */
    net_use_weight?: number
    /**
     * 主单id
     */
    production_notify_order_id?: number
    /**
     * 组合描述
     */
    remark?: string
    /**
     * 排序标识（备用）
     */
    sort_mark?: string
    /**
     * 来源项id
     */
    src_id?: number
    /**
     * 总用量
     */
    total_use_weight?: number
    /**
     * 修改时间
     */
    update_time?: string
    /**
     * 修改人
     */
    update_user_name?: string
    /**
     * 修改人
     */
    updater_id?: number
    [property: string]: any
  }

  /**
   * produce.WeavingSpecifications
   */
  export interface ProduceWeavingSpecifications {
  /**
   * 织造规格ID
   */
    weaving_specifications_id?: number
    /**
     * 织造规格名称
     */
    weaving_specifications_name?: string
    [property: string]: any
  }
}
