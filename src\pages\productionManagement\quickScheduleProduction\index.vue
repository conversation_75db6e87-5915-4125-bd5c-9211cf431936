<script setup lang="ts">
import { onActivated, onMounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import ShutdownRemarkDialog, { type ShutdownRemarkData } from './components/ShutdownRemarkDialog.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import FildCard from '@/components/FildCard.vue'
import Table from '@/components/Table.vue'
import SelectDate from '@/components/SelectDate/index.vue'
import PrintPopoverBtn from '@/components/PrintPopoverBtn/index.vue'
import MachineSchedulingDialog from '@/pages/productionManagement/productionNotice/components/MachineSchedulingDialog.vue'
import FabricFlyCancelDialog from '@/pages/grayFabricMange/greyClothTicketCancel/components/FabricFlyCancelDialog.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'
import PrintRecordDialog from '@/pages/productionManagement/scheduleProduction/components/PrintRecordDialog.vue'
import {
  GetQuickScheduleProductionList,
  UpdateMachineStopStatus,
} from '@/api/quickScheduleProduction'
import { debounce, getFilterData } from '@/common/util'
import { usePrintTemplate } from '@/components/PrintPopoverBtn/index'
import { PrintDataType, PrintType } from '@/components/PrintPopoverBtn/types'
import { BusinessUnitIdEnum } from '@/common/enum'
import { GetFineCodeList, GetFineCodePrintRecord, PrintFineCode } from '@/api/scheduleProduction'

const { options } = usePrintTemplate({
  printType: PrintType.PrintTemplateTypeStock,
  dataType: PrintDataType.Grey,
})

// 远程搜索状态
const componentRemoteSearch = reactive({
  name: '',
})

const state = reactive<any>({
  tableData: [],
  filterData: {
    // 排产日期范围
    schedule_date_start: '',
    schedule_date_end: '',
    // 排产单号
    order_no: '',
    // 营销体系ID
    sale_system_id: '',
    // 织厂ID
    biz_unit_id: '',
    // 客户ID
    customer_id: '',
    // 机台号
    machine_number: '',
    // 用料批号
    material_batch_numbers: '',
    // 布飞状态(1未打印、2部分打印、3已打印)
    print_status: '',
  },
  multipleSelection: [],
  // 当前选中的行数据
  currentSelectedRow: null,
  // 是否只显示未打印条码
  isShow: false,
  statistics: {
    total_count: 0,
    pending_count: 0,
    producing_count: 0,
    completed_count: 0,
    cancelled_count: 0,
    avg_completion_time: 0,
    machine_utilization: 0,
  },
})

// 机台排产对话框状态
const machineSchedulingDialogVisible = ref(false)
const currentProductionData = ref<any>({})
const dialogMode = ref<'add' | 'edit'>('add')

// 布飞取消对话框状态
const fabricFlyCancelDialogVisible = ref(false)

// 停机备注对话框状态
const shutdownRemarkDialogVisible = ref(false)

// 打印记录弹窗状态
const printRecordDialogVisible = ref(false)
const selectedFineCodeId = ref<number>(0)
const selectedFineCodeName = ref<string>('')

const {
  fetchData: ApiQuickScheduleList,
  data: datalist,
  total,
  success,
  msg,
  loading,
  page,
  size,
  handleSizeChange,
  handleCurrentChange,
} = GetQuickScheduleProductionList()
const { fetchData: updateMachineStopFetch, success: updateMachineStopSuccess, msg: updateMachineStopMsg } = UpdateMachineStopStatus()

// 打印相关API
const { fetchData: printFineCodeData, loading: printLoading } = PrintFineCode()
// 首次加载数据
onMounted(() => {
  getData()
})

onActivated(() => {
  getData()
})

// 快速排产单表格列配置
const columnList = ref([
  {
    sortable: true,
    field: 'sale_system_name',
    title: '营销部门',
    fixed: 'left' as const,
    width: '120px',
  },
  {
    sortable: true,
    field: 'weave_factory_name',
    title: '织厂名称',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'customer_name',
    title: '客户名称',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'schedule_date',
    title: '排产日期',
    minWidth: 120,
    isDate: true,
  },
  {
    sortable: true,
    field: 'order_no',
    title: '排产单号',
    minWidth: 140,
  },
  {
    sortable: true,
    field: 'production_notify_order_no',
    title: '生产通知单',
    minWidth: 140,
  },
  {
    sortable: true,
    field: 'grey_fabric_name',
    title: '坯布名称',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'grey_fabric_color_name',
    title: '坯布颜色',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'machine_number',
    title: '机台号',
    minWidth: 80,
  },
  {
    sortable: true,
    field: 'yarn_brand',
    title: '纱牌',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'yarn_batch',
    title: '纱批',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'yarn_name',
    title: '纱名',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'material_batch_numbers',
    title: '材料批号',
    minWidth: 120,
  },
  {
    sortable: true,
    field: 'produced_roll',
    title: '生产条数',
    minWidth: 100,
    isPrice: true,
  },
  {
    sortable: true,
    field: 'schedule_roll',
    title: '排产条数',
    minWidth: 100,
    isPrice: true,
  },
  {
    sortable: true,
    field: 'in_warehouse_weight',
    title: '进仓数量',
    minWidth: 100,
    isWeight: true,
  },
  {
    sortable: true,
    field: 'in_warehouse_roll',
    title: '进仓条数',
    minWidth: 100,
    isPrice: true,
  },
  {
    sortable: true,
    field: 'weighing_weight',
    title: '称重数量',
    minWidth: 100,
    isWeight: true,
  },
  {
    sortable: true,
    field: 'weighing_roll',
    title: '称重条数',
    minWidth: 100,
    isPrice: true,
  },
  {
    sortable: true,
    field: 'inspection_weight',
    title: '验布数量',
    minWidth: 100,
    isWeight: true,
  },
  {
    sortable: true,
    field: 'inspection_roll',
    title: '验布条数',
    minWidth: 100,
    isPrice: true,
  },
  {
    sortable: true,
    field: 'un_produce_roll',
    title: '未织条数',
    minWidth: 100,
    isPrice: true,
  },
  {
    sortable: true,
    field: 'out_warehouse_weight',
    title: '出仓数量',
    minWidth: 100,
    isWeight: true,
  },
  {
    sortable: true,
    field: 'out_warehouse_roll',
    title: '出仓条数',
    minWidth: 100,
    isPrice: true,
  },
  {
    sortable: true,
    field: 'stock_weight',
    title: '库存数量',
    minWidth: 100,
    isWeight: true,
  },
  {
    sortable: true,
    field: 'stock_roll',
    title: '库存条数',
    minWidth: 100,
    isPrice: true,
  },
  {
    sortable: true,
    field: 'printed_roll',
    title: '已打布飞',
    minWidth: 100,
    isPrice: true,
  },
  {
    sortable: true,
    field: 'un_print_roll',
    title: '未打布飞',
    minWidth: 100,
    isPrice: true,
  },
  {
    sortable: true,
    field: 'needle_size',
    title: '针寸数',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'total_needle_size',
    title: '总针数',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'loom_model_name',
    title: '织造方式',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'creator_name',
    title: '创建人',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'create_time',
    title: '创建时间',
    minWidth: 100,
    isDate: true,
  },
  {
    sortable: true,
    field: 'auditor_name',
    title: '审核人',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'audit_date',
    title: '审核时间',
    minWidth: 100,
    isDate: true,
  },
  {
    sortable: true,
    field: 'printer_name',
    title: '布飞打印人',
    minWidth: 100,
  },
  {
    sortable: true,
    field: 'print_time',
    title: '布飞打印时间',
    minWidth: 100,
    isDate: true,
  },
  {
    sortable: true,
    field: 'print_status',
    title: '布飞打印状态',
    minWidth: 100,
    soltName: 'print_status',
  },
  {
    sortable: true,
    field: 'machine_stop_date',
    title: '停机日期',
    minWidth: 100,
    isDate: true,
  },
  {
    sortable: true,
    field: 'machine_stop_status',
    title: '停机状态',
    minWidth: 100,
    soltName: 'machine_stop_status',
  },
  {
    sortable: true,
    field: 'machine_stop_remark',
    title: '停机备注',
    minWidth: 120,
  },
])

// 获取数据
async function getData() {
  try {
    await ApiQuickScheduleList(getFilterData(state.filterData))

    // 检查数据是否获取成功
    if (success.value) {
      state.tableData = datalist.value.list || []
    }
    else {
      state.tableData = []
      ElMessage.warning(msg.value)
    }
  }
  catch (error) {
    console.error('获取快速排产单列表失败:', error)
    ElMessage.error('获取数据失败，请稍后重试')
    state.tableData = []
  }
}

// 搜索防抖
const searchDebounce = debounce(() => {
  getData()
}, 300)

// 监听搜索条件变化
watch(() => state.filterData, () => {
  searchDebounce()
}, { deep: true })

const config = reactive({
  loading,
  showPagition: true,
  showSlotNums: true,
  page,
  size,
  total,
  showCheckBox: true,
  showOperate: true,
  operateWidth: '180',
  height: 'auto',
  showSort: false,
  handleSizeChange: (val: number) => handleSizeChange(val),
  handleCurrentChange: (val: number) => handleCurrentChange(val),
  handAllSelect: (val: any) => handAllSelect(val),
  handleSelectionChange: (val: any) => handleSelectionChange(val),
  cellClick: (val: any) => handleCellClick(val),
})

function handAllSelect({ records }: any) {
  state.multipleSelection = records
}

function handleSelectionChange({ records }: any) {
  state.multipleSelection = records
}

// 处理机台排产对话框保存
function handleMachineSchedulingSave(data: any) {
  console.log('保存机台排产数据:', data)
  ElMessage.success('机台排产保存成功')
  machineSchedulingDialogVisible.value = false
  // 刷新列表数据
  getData()
}

// 打开布飞取消对话框
function handleFabricFlyCancel() {
  fabricFlyCancelDialogVisible.value = true
}

// 处理布飞取消成功
function handleFabricFlyCancelSuccess(data: any) {
  console.log('布飞取消成功:', data)
  ElMessage.success(`条码 ${data.barcode} 布飞取消成功`)
  // 刷新列表数据
  getData()
}
// 新建
function handleAdd() {
  currentProductionData.value = {}
  dialogMode.value = 'add'
  machineSchedulingDialogVisible.value = true
}

// 打印
function handlePrint(row: any) {
  console.log('打印排产单:', row)
  ElMessage.success('打印功能开发中...')
  // TODO: 实现打印功能
}

// 编辑
function handleEdit(row: any) {
  currentProductionData.value = { ...row }
  dialogMode.value = 'edit'
  machineSchedulingDialogVisible.value = true
}

// 当前选中的排产单数据
const currentMachineStopRow = ref<any>(null)

// 停机备注
function handleShutdownRemark(row: any) {
  currentMachineStopRow.value = row
  shutdownRemarkDialogVisible.value = true
}

// 停机备注确认处理
async function handleShutdownRemarkConfirm(data: ShutdownRemarkData) {
  try {
    if (!currentMachineStopRow.value) {
      ElMessage.error('请先选择要停机的排产单')
      return
    }

    // 调用停机状态更新接口
    await updateMachineStopFetch({
      id: currentMachineStopRow.value.id,
      machine_stop_status: data.isShutdown,
      machine_stop_date: data.shutdownDate,
      machine_stop_remark: data.remark,
    })

    if (updateMachineStopSuccess.value) {
      ElMessage.success('停机状态更新成功')
      // 重新获取数据
      getData()
      // 关闭弹窗
      shutdownRemarkDialogVisible.value = false
      // 清空当前选中行
      currentMachineStopRow.value = null
    }
    else {
      ElMessage.error(updateMachineStopMsg.value || '停机状态更新失败')
    }
  }
  catch (error) {
    console.error('停机状态更新失败:', error)
    ElMessage.error('停机状态更新失败')
  }
}
const { fetchData: fetchFineCodeData, data: fineCodeData, loading: fineCodeLoading } = GetFineCodeList()

async function handleCellClick({ row }: any) {
  try {
    // 保存当前选中的行数据
    state.currentSelectedRow = row

    // 构建请求参数
    const params: any = {
      production_schedule_order_id: row.id,
    }

    // 如果勾选了"显示未打印条码"，则只获取未打印的数据
    if (state.isShow)
      params.print_status = 1 // 1表示未打印

    // 根据生产排产单ID获取细码列表
    await fetchFineCodeData(params)
  }
  catch (error) {
    console.error('获取细码列表失败:', error)
  }
}
const tableConfig2 = ref({
  loading: fineCodeLoading,
  showPagition: false,
  showSlotNums: true,
  showCheckBox: true,
  showOperate: true,
  height: '100%',
  operateWidth: '150', // 增加操作列宽度以容纳两个按钮
  showSort: false,
  checkboxConfig: {
    checkField: 'selected',
    highlight: true,
    reserve: true,
  },
})
const columnList2 = ref([
  {
    field: 'machine_number',
    title: '机台',
    minWidth: 100,
  },
  {
    field: 'fabric_piece_code',
    title: '条形码',
    minWidth: 150,
  },
  {
    field: 'volume_number',
    title: '卷号',
    minWidth: 100,
  },
  {
    field: 'grey_fabric_level_id',
    title: '坯布等级',
    minWidth: 100,
  },
  {
    field: 'quality_remark',
    title: '质量备注',
    minWidth: 150,
  },
  {
    field: 'in_warehouse_weight',
    title: '进仓数量',
    isWeight: true,
    minWidth: 100,
  },
  {
    field: 'in_warehouse_roll',
    title: '进仓条数',
    isPrice: true,
    minWidth: 100,
  },
  {
    field: 'weighing_weight',
    title: '称重数量',
    isWeight: true,
    minWidth: 100,
  },
  {
    field: 'weighing_roll',
    title: '称重条数',
    isPrice: true,
    minWidth: 100,
  },
  {
    field: 'inspection_weight',
    title: '验布数量',
    isWeight: true,
    minWidth: 100,
  },
  {
    field: 'inspection_roll',
    title: '验布条数',
    isPrice: true,
    minWidth: 100,
  },
  {
    field: 'out_warehouse_weight',
    title: '出仓数量',
    isWeight: true,
    minWidth: 100,
  },
  {
    field: 'out_warehouse_roll',
    title: '出仓条数',
    isPrice: true,
    minWidth: 100,
  },
  {
    field: 'stock_weight',
    title: '库存数量',
    isWeight: true,
    minWidth: 100,
  },
  {
    field: 'stock_roll',
    title: '库存条数',
    isPrice: true,
    minWidth: 100,
  },
  {
    field: 'printer_name',
    title: '打印人',
    minWidth: 100,
  },
  {
    field: 'print_time',
    title: '打印时间',
    isDate: true,
    minWidth: 150,
  },
  {
    field: 'print_status',
    slot: 'print_status',
    title: '打印状态',
    minWidth: 100,
  },
  {
    field: 'operation',
    title: '操作',
    minWidth: 150,
    slot: 'operate',
  },
])
// 获取打印状态样式类
function getPrintStatusClass(status: number) {
  switch (status) {
    case 1:
      return 'yuan_red' // 未打印
    case 2:
      return 'yuan_yellow' // 部分打印
    case 3:
      return 'yuan' // 已打印
    default:
      return 'yuan_red'
  }
}

// 获取打印状态文字样式类
function getPrintStatusTextClass(status: number) {
  switch (status) {
    case 1:
      return 'yuan_font_active' // 未打印
    case 2:
      return 'yuan_font_warning' // 部分打印
    case 3:
      return 'yuan_font' // 已打印
    default:
      return 'yuan_font_active'
  }
}

// 获取打印状态文字
function getPrintStatusText(status: number) {
  switch (status) {
    case 1:
      return '未打印'
    case 2:
      return '部分打印'
    case 3:
      return '已打印'
    default:
      return '未打印'
  }
}

// 打印单个细码
async function handlePrintSingle(row: any) {
  try {
    console.log('准备打印单个细码ID:', row.id)

    // 调用打印接口
    await printFineCodeData({
      ids: row.id.toString(),
      remark: `打印单个细码: ${row.fabric_piece_code || row.id}`,
    })

    ElMessage.success('打印成功')

    // 打印成功后重新获取数据，更新打印状态
    await refreshFineCodeData()
  }
  catch (error) {
    console.error('打印单个细码失败:', error)
    ElMessage.error('打印失败，请重试')
  }
}

// 刷新细码数据
async function refreshFineCodeData() {
  if (state.currentSelectedRow?.id) {
    // 重新获取细码数据
    const params: any = {
      production_schedule_order_id: state.currentSelectedRow.id,
    }

    // 如果勾选了"显示未打印条码"，则只获取未打印的数据
    if (state.isShow)
      params.print_status = 1

    await fetchFineCodeData(params)
  }
}

// 打开打印记录弹窗
function openPrintRecordDialog(row: any) {
  selectedFineCodeId.value = row.id
  selectedFineCodeName.value = row.fabric_piece_code || `细码-${row.id}`
  printRecordDialogVisible.value = true
}

function handlePrintAll() {

}
const showUnprint = ref(false)
</script>

<template>
  <div class="list-page">
    <!-- 搜索表单 -->
    <FildCard title="" :tool-bar="false">
      <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
        <DescriptionsFormItem label="排产日期:">
          <template #content>
            <SelectDate
              v-model:start-date="state.filterData.schedule_date_start"
              v-model:end-date="state.filterData.schedule_date_end"
              placeholder="请选择排产日期范围"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="营销部门:">
          <template #content>
            <SelectComponents v-model="state.filterData.sale_system_id" label-field="name" value-field="id" api="GetSaleSystemDropdownListApi" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="织厂名称:">
          <template #content>
            <SelectDialog
              v-model="state.filterData.biz_unit_id"
              api="business_unitlist"
              :query="{
                unit_type_id: BusinessUnitIdEnum.knittingFactory,
                name: componentRemoteSearch.name,
              }"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @change-input="(val) => (componentRemoteSearch.name = val)"
            />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="客户名称:">
          <template #content>
            <SelectCustomerDialog v-model="state.filterData.customer_id" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="机台号:">
          <template #content>
            <el-input v-model="state.filterData.machine_number" clearable placeholder="机台号" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="用料批号:">
          <template #content>
            <el-input v-model="state.filterData.material_batch_numbers" clearable placeholder="用料批号" />
          </template>
        </DescriptionsFormItem>
        <DescriptionsFormItem label="布飞状态:">
          <template #content>
            <SelectComponents
              v-model="state.filterData.print_status"
              api="EnumPrintStatus"
              label-field="name"
              value-field="id"
              clearable
            />
          </template>
        </DescriptionsFormItem>
      </div>
    </FildCard>

    <!-- 操作按钮 -->
    <FildCard title="" class="table-card-full" :tool-bar="false">
      <template #right-top>
        <el-button type="primary" :icon="Plus" @click="handleAdd">
          新增布飞排产
        </el-button>
        <!--        <el-button -->
        <!--          type="primary" -->
        <!--          @click="handleShutdownRemark" -->
        <!--        > -->
        <!--          停机备注 -->
        <!--        </el-button> -->
        <el-button
          type="primary"
          @click="handleFabricFlyCancel"
        >
          布飞取消
        </el-button>
        <PrintPopoverBtn
          :options="options"
          :data="state.tableData"
          print-btn-text="排产布飞打印"
        />
      </template>
      <!-- 表格 -->
      <Table
        :table-list="state.tableData"
        :column-list="columnList"
        :config="config"
      >
        <template #print_status="{ row }">
          <div class="flex items-center">
            <div :class="getPrintStatusClass(row.print_status)" />
            <div :class="getPrintStatusTextClass(row.print_status)">
              {{ getPrintStatusText(row.print_status) }}
            </div>
          </div>
        </template>
        <template #machine_stop_status="{ row }">
          <div class="flex items-center">
            <div :class="row.machine_stop_status ? 'yuan_red' : 'yuan'" />
            <div :class="row.machine_stop_status ? 'yuan_font_active' : 'yuan_font'">
              {{ row.machine_stop_status ? "已停机" : "正常" }}
            </div>
          </div>
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              type="primary"
              :underline="false"
              :disabled="row.print_status === 2"
              @click="handlePrint(row)"
            >
              {{ row.print_status === 2 ? '已打印' : '打印' }}
            </el-link>
            <el-link type="primary" size="small" :underline="false" @click="handleEdit(row)">
              编辑
            </el-link>
            <el-link
              type="primary"
              size="small"
              :underline="false"
              @click="handleShutdownRemark(row)"
            >
              停机备注
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>
    <FildCard :tool-bar="false" class="table-card-bottom">
      <div>
        <el-button type="primary" plain @click="handlePrintAll">
          打印布飞
        </el-button>
        <el-checkbox type="primary" class="ml-2" @click="showUnprint = !showUnprint">
          显示未打印条码
        </el-checkbox>
        <span class="ml-4 text-gray-500">
          提示：双击上方表格行可查看对应的细码列表
        </span>
      </div>

      <Table
        :config="tableConfig2"
        :table-list="fineCodeData?.list"
        :column-list="columnList2"
      >
        <template #print_status="{ row }">
          <div class="flex items-center">
            <div :class="row.print_status === 1 ? 'yuan_red' : 'yuan'" />
            <div :class="row.print_status === 1 ? 'yuan_font_active' : 'yuan_font'">
              {{ row.print_status === 1 ? "未打印" : "已打印" }}
            </div>
          </div>
        </template>
        <template #operate="{ row }">
          <el-space :size="10">
            <el-link
              type="primary"
              :underline="false"
              :disabled="row.print_status === 2"
              @click="handlePrintSingle(row)"
            >
              {{ row.print_status === 2 ? '已打印' : '打印' }}
            </el-link>
            <el-link
              type="success"
              :underline="false"
              @click="openPrintRecordDialog(row)"
            >
              打印记录
            </el-link>
          </el-space>
        </template>
      </Table>
    </FildCard>

    <!-- 机台排产对话框 -->
    <MachineSchedulingDialog
      v-model="machineSchedulingDialogVisible"
      :production-data="currentProductionData"
      :mode="dialogMode"
      @save="handleMachineSchedulingSave"
    />

    <!-- 布飞取消对话框 -->
    <FabricFlyCancelDialog
      v-model="fabricFlyCancelDialogVisible"
      title="布飞取消"
      @success="handleFabricFlyCancelSuccess"
    />

    <!-- 停机备注对话框 -->
    <ShutdownRemarkDialog
      v-model="shutdownRemarkDialogVisible"
      title="停机备注"
      @confirm="handleShutdownRemarkConfirm"
    />

    <!-- 打印记录弹窗 -->
    <PrintRecordDialog
      v-model="printRecordDialogVisible"
      :fine-code-id="selectedFineCodeId"
      :fine-code-name="selectedFineCodeName"
    />
  </div>
</template>

<style lang="scss" scoped>
.quick-schedule-production {
  .statistics-cards {
    margin-bottom: 20px;

    .stat-card {
      border-radius: 8px;
      border: none;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      &.pending {
        border-left: 4px solid #e6a23c;
      }

      &.producing {
        border-left: 4px solid #409eff;
      }

      &.completed {
        border-left: 4px solid #67c23a;
      }

      &.cancelled {
        border-left: 4px solid #f56c6c;
      }

      &.utilization {
        border-left: 4px solid #909399;
      }

      .stat-content {
        text-align: center;

        .stat-number {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 8px;
        }

        .stat-label {
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }

  // 打印状态样式
  .yuan {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #67c23a;
    margin-right: 5px;
  }

  .yuan_red {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #f56c6c;
    margin-right: 5px;
  }

  .yuan_yellow {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #e6a23c;
    margin-right: 5px;
  }

  .yuan_font {
    color: #67c23a;
    font-size: 12px;
  }

  .yuan_font_active {
    color: #f56c6c;
    font-size: 12px;
  }

  .yuan_font_warning {
    color: #e6a23c;
    font-size: 12px;
  }
}
</style>
