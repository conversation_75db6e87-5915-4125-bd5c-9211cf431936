<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import SelectCustomerDialog from '@/components/SelectCustomerDialog/index.vue'
import Table from '@/components/Table.vue'
import SelectDialog from '@/components/SelectDialog/index.vue'
import SelectProductionNotice from '@/pages/procurementManaging/rawMaterialSourcing/components/SelectProductionNotice/index.vue'
import { BusinessUnitIdEnum, DictionaryType } from '@/common/enum'
import SelectComponents from '@/components/SelectComponents/index.vue'
import { formatHashTag, formatRateMul, formatRollDiv } from '@/common/format'
import { AddProductionScheduleOrder, UpdateProductionScheduleOrder } from '@/api/productionNotice'
// Props 定义
interface Props {
  modelValue: boolean
  productionData?: any
  mode?: 'add' | 'edit' // 添加模式属性
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  productionData: () => ({}),
  mode: 'add', // 默认为添加模式
})

// Emits 定义
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'save': [data: any]
}>()
const componentRemoteSearch = reactive({
  name: '',
  customer_name: '',
  unit_name: '',
  raw_name: '',
  raw_code: '',
})
// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})
const { fetchData: addProductionScheduleOrder, success, msg, data: addData } = AddProductionScheduleOrder()
const { fetchData: updateProductionScheduleOrder, success: updateSuccess, msg: updateMsg } = UpdateProductionScheduleOrder()

// 计算属性：是否为编辑模式
const isEditMode = computed(() => props.mode === 'edit')

// 计算属性：弹窗标题
const dialogTitle = computed(() => isEditMode.value ? '机台排产编辑' : '机台排产添加')

const formRef = ref()
const formData = reactive({
  id: null, // 编辑模式需要的ID字段
  unit_id: '',
  unit_name: '',
  customer_id: null,
  grey_fabric_id: null,
  production_number: '',
  machine_number: '',
  schedule_count: '',
  yarn_batch: '',
  contract_number: '1',
  material_batch: '',
  contract_remark: '',
  yarn_brand: '',
  grey_fabric_number: '',
  grey_fabric_name: '',
  needle_count: '',
  grey_fabric_color: '',
  customer_name: '',
  gray_fabric_color_id: null,
  customer_code: '',
  style_no: '',
  yarn_name: '',
  produced_roll: 0,
  scheduled_count: 0,
  total_scheduled_roll: 0,
  total_un_schedule_roll: 0,
  // 新增API需要的字段
  bf_prefix: '',
  bf_sequence_number_rule: null,
  fabric_finishing: '',
  production_notify_order_id: null,
  sale_system_id: null,
  schedule_date: '',
  total_needle_size: '',
})

// 选择生产通知单弹窗相关
const productionNoticeModalVisible = ref(false)

// 打开选择生产通知单弹窗
const productionNoticeModalRef = ref()
function openProductionNoticeModal() {
  if (!formData.unit_id) {
    ElMessage.warning('请先选择织厂')
    return
  }
  productionNoticeModalRef.value.state.filterData.weaving_mill_id = formData.unit_id
  productionNoticeModalVisible.value = true
}

// 原料列表
const materialList = ref([])
// 处理生产通知单选择
async function handleProductionNoticeSubmit(selectedData: any) {
  if (!selectedData) {
    ElMessage.warning('请选择一个生产通知单')
    return
  }

  // 将选中的生产通知单信息填充到表单中
  formData.production_number = selectedData.order_no
  formData.grey_fabric_name = selectedData.grey_fabric_name
  formData.grey_fabric_code = selectedData.grey_fabric_code
  formData.grey_fabric_color = selectedData.grey_fabric_color_name || ''
  formData.grey_fabric_id = selectedData.grey_fabric_id
  formData.gray_fabric_color_id = selectedData.grey_fabric_color_id
  formData.customer_id = selectedData.customer_id
  formData.customer_name = selectedData.customer_name
  formData.production_notify_order_id = selectedData.id
  formData.needle_count = selectedData.needle_size
  formData.unit_id = selectedData.weaving_mill_id
  formData.unit_name = selectedData.weaving_mill_name
  formData.sale_system_id = selectedData.sale_system_id
  formData.contract_remark = selectedData.order_remark
  formData.un_schedule_roll = selectedData.un_schedule_roll
  formData.scheduled_roll = selectedData.scheduled_roll
  formData.produced_roll = selectedData.produced_roll

  // 处理原料比例信息
  if (selectedData.production_notify_material_ratio && Array.isArray(selectedData.production_notify_material_ratio)) {
    materialList.value = selectedData.production_notify_material_ratio.map((item: Api.ProductionNoticeOrderDownList.ProduceGetProductionNotifyMaterialRatioData) => ({
      raw_material_code: item.raw_material_code || '',
      raw_material_name: item.raw_material_name || '',
      raw_material_id: item.raw_material_id || 0,
      weaving_category: item.weaving_category || '',
      supplier_name: item.supplier_name || '',
      supplier_id: item.supplier_id || 0,
      brand: item.raw_material_brand || '',
      batch_num: item.raw_material_batch_number || '',
      dyelot_number: '', // 这个字段在类型定义中没有，设为空字符串
      color_id: item.grey_fabric_color_id, // 这个字段在类型定义中没有，设为0
      color_name: item.grey_fabric_color_name || '',
      yarn_ratio: item.yarn_ratio || 0,
      raw_material_loss: item.yarn_loss || 0,
      production_notify_order_detail_id: item.id || 0,
    }))
  }

  productionNoticeModalVisible.value = false
  ElMessage.success('生产通知单选择成功')
}

// 处理织厂选择变化
async function handleUnitChange(selectedUnit: any) {
  if (!selectedUnit)
    return

  // 更新织厂信息
  formData.unit_name = selectedUnit.name
  formData.unit_id = selectedUnit.id
  formData.bf_prefix = selectedUnit.bf_prefix
}

// 计算 生产 - 排产 = 未排条数
watch([() => formData.produced_roll, () => formData.scheduled_count], () => {
  formData.total_un_schedule_roll = (formData.produced_roll || 0) - (formData.scheduled_count || 0)
}, { immediate: true })

// 监听弹窗显示状态，在新增模式下重置数据
watch(() => props.modelValue, (newValue, oldValue) => {
  if (newValue && !oldValue) {
    // 弹窗从关闭到打开
    if (!isEditMode.value) {
      // 新增模式：重置表单数据
      resetFormData()
    }
  }
}, { immediate: false })

// 监听弹窗显示状态，处理初始化
watch(() => visible.value, (newVisible) => {
  if (newVisible) {
    // 弹窗打开时，如果是新增模式且没有传入数据，则重置表单
    if (!isEditMode.value && (!props.productionData || Object.keys(props.productionData).length === 0))
      resetFormData()
  }
}, { immediate: true })

// 监听原料信息变化，自动更新用料批号和纱牌
watch(() => materialList.value, () => {
  // 更新用料批号：组合所有原料批号
  const materialBatches = materialList.value
    .map((item: any) => item.batch_num || item.material_batch)
    .filter((batch: string) => batch && batch.trim() !== '')
  formData.material_batch = materialBatches.join('+')

  // 更新纱牌：组合所有原料品牌
  const materialBrands = materialList.value
    .map((item: any) => item.brand || item.material_brand)
    .filter((brand: string) => brand && brand.trim() !== '')
  formData.yarn_brand = materialBrands.join('+')
}, { deep: true, immediate: true })

// 表单验证规则
const rules = {
  unit_id: [{ required: true, message: '请选择织厂名称', trigger: 'change' }],
  production_number: [{ required: true, message: '请输入生产单号', trigger: 'change' }],
  machine_number: [{ required: true, message: '请输入机台号', trigger: 'change' }],
  scheduled_count: [{ required: true, message: '请输入排产条数', trigger: 'change' }],
}

// 表格配置
const tableConfig = ref({
  showPagition: false,
  showSlotNums: true,
  showCheckBox: false,
  showOperate: false,
  height: 300,
  operateWidth: '100',
  showSort: false,
})

const columnList = ref([
  { field: 'raw_material_code', title: '原料编号' },
  { field: 'raw_material_name', title: '原料名称' },
  { field: 'weaving_category', title: '织造类别', soltName: 'weaving_category' },
  { field: 'supplier_name', title: '供方名称', soltName: 'supplier_name' },
  { field: 'brand', title: '原料品牌', soltName: 'brand' },
  { field: 'batch_num', title: '原料批号', soltName: 'batch_num' },
  { field: 'dyelot_number', title: '原料缸号', soltName: 'dyelot_number' },
  { field: 'color_id', title: '原料颜色', soltName: 'color_id' },
  { field: 'yarn_ratio', title: '原料比例(%)', isPrice: true },
  { field: 'raw_material_loss', title: '原料损耗(%)', isPrice: true },
])

// 重置表单数据
function resetFormData() {
  Object.assign(formData, {
    id: null,
    unit_id: '',
    unit_name: '',
    customer_id: null,
    grey_fabric_id: null,
    production_number: '',
    machine_number: '',
    schedule_count: '',
    yarn_batch: '',
    contract_number: '',
    material_batch: '',
    contract_remark: '',
    yarn_brand: '',
    grey_fabric_number: '',
    grey_fabric_name: '',
    needle_count: '',
    grey_fabric_color: '',
    customer_name: '',
    gray_fabric_color_id: null,
    customer_code: '',
    style_no: '',
    yarn_name: '',
    produced_roll: 0,
    scheduled_count: 0,
    total_scheduled_roll: 0,
    total_un_schedule_roll: 0,
    bf_prefix: '',
    bf_sequence_number_rule: null,
    fabric_finishing: '',
    production_notify_order_id: null,
    sale_system_id: null,
    schedule_date: '',
    total_needle_size: '',
  })

  // 重置原料列表
  materialList.value = []
}

// 方法定义
function handleClose() {
  visible.value = false
  // 关闭时重置表单数据，确保下次打开是干净状态
  resetFormData()
}

async function handleSaveAndExit() {
  try {
    await formRef.value?.validate()

    // 构建原料比例列表
    const materialRatioList: Api.AddProductionScheduleOrder.ProduceAddProductionScheduleOrderMaterialRatioParam[] = materialList.value.map((item: any) => ({
      material_ratio: item.yarn_ratio || 0,
      material_loss: item.raw_material_loss || 0,
      production_notify_order_detail_id: item.production_notify_order_detail_id || 0,
      raw_material_batch_number: item.batch_num || '',
      raw_material_brand: item.brand || '',
      raw_material_color_id: item.color_id || 0,
      raw_material_color_name: item.color_name || '',
      raw_material_dyelot_number: item.dyelot_number || '',
      raw_material_id: item.raw_material_id || 0,
      raw_material_name: item.raw_material_name || '',
      supplier_id: item.supplier_id || 0,
      weaving_category_id: item.weaving_category_id || 0,
    }))

    if (isEditMode.value) {
      // 编辑模式：构建更新请求参数
      const updateRequestData: Api.UpdateProductionScheduleOrder.Request = {
        id: formData.id || props.productionData?.id,
        account_num: formData.style_no || '',
        bf_prefix: formData.bf_prefix || '',
        bf_sequence_number_rule: formData.bf_sequence_number_rule || undefined,
        customer_id: formData.customer_id || undefined,
        fabric_finishing: formData.fabric_finishing || '',
        grey_fabric_color_id: formData.gray_fabric_color_id || undefined,
        grey_fabric_id: formData.grey_fabric_id || undefined,
        machine_number: formData.machine_number || '',
        material_ratios: materialRatioList,
        needle_size: formData.needle_count || '',
        production_notify_order_id: formData.production_notify_order_id || undefined,
        production_notify_order_no: formData.production_number || '',
        remark: formData.contract_remark || '',
        sale_system_id: formData.sale_system_id || undefined,
        schedule_date: formData.schedule_date || '',
        schedule_roll: formatRateMul(formData.scheduled_count) || 0,
        total_needle_size: formData.total_needle_size || '',
        weave_factory_id: typeof formData.unit_id === 'string' ? undefined : formData.unit_id,
        yarn_batch: formData.yarn_batch || '',
      }

      // 调用更新API
      await updateProductionScheduleOrder(updateRequestData)

      if (!updateSuccess.value)
        return ElMessage.error(updateMsg.value)

      emit('save', { ...formData, materials: materialList.value, id: updateRequestData.id })
      ElMessage.success('更新成功')
      handleClose()
    }
    else {
      // 新增模式：构建新增请求参数
      const addRequestData: Api.AddProductionScheduleOrder.Request = {
        account_num: formData.style_no || '',
        bf_prefix: formData.bf_prefix || '',
        bf_sequence_number_rule: formData.bf_sequence_number_rule || undefined,
        customer_id: formData.customer_id || undefined,
        fabric_finishing: formData.fabric_finishing || '',
        grey_fabric_color_id: formData.gray_fabric_color_id || undefined,
        grey_fabric_id: formData.grey_fabric_id || undefined,
        machine_number: formData.machine_number || '',
        material_ratios: materialRatioList,
        needle_size: formData.needle_count || '',
        production_notify_order_id: formData.production_notify_order_id || undefined,
        production_notify_order_no: formData.production_number || '',
        remark: formData.contract_remark || '',
        sale_system_id: formData.sale_system_id || undefined,
        schedule_date: formData.schedule_date || '',
        schedule_roll: formatRateMul(formData.scheduled_count) || 0,
        total_needle_size: formData.total_needle_size || '',
        weave_factory_id: typeof formData.unit_id === 'string' ? undefined : formData.unit_id,
        yarn_batch: formData.yarn_batch || '',
      }

      // 调用新增API
      await addProductionScheduleOrder(addRequestData)

      if (!success.value)
        return ElMessage.error(msg.value)

      emit('save', { ...formData, materials: materialList.value, id: addData.value?.id })
      ElMessage.success('保存成功')
      handleClose()
    }
  }
  catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败，请检查表单填写')
  }
}
// 选择原料颜色
function changeColor(item: any, row: any) {
  row.color_name = item.name
  row.color_code = item.code
}
// 选择客户
async function customerChange(val: any) {
  formData.customer_name = val?.name
  formData.customer_code = val?.code
}
// 监听生产数据变化
watch(() => props.productionData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    // 正确映射API返回的字段到表单字段
    const mappedData = {
      id: newData.id,
      unit_id: newData.weave_factory_id,
      unit_name: newData.weave_factory_name,
      customer_id: newData.customer_id,
      customer_name: newData.customer_name,
      grey_fabric_id: newData.grey_fabric_id,
      grey_fabric_name: newData.grey_fabric_name,
      gray_fabric_color_id: newData.grey_fabric_color_id,
      grey_fabric_color: newData.grey_fabric_color_name,
      production_number: newData.production_notify_order_no,
      production_notify_order_id: newData.production_notify_order_id,
      machine_number: newData.machine_number,
      yarn_batch: newData.yarn_batch,
      contract_number: newData.contract_sequence,
      material_batch: newData.material_batch_numbers,
      contract_remark: newData.remark,
      yarn_brand: newData.yarn_brand,
      yarn_name: newData.yarn_name,
      needle_count: newData.needle_size,
      style_no: newData.account_num,
      scheduled_count: formatRollDiv(newData.schedule_roll),
      schedule_date: newData.schedule_date,
      total_needle_size: newData.total_needle_size,
      fabric_finishing: newData.fabric_finishing,
      sale_system_id: newData.sale_system_id,
      produced_roll: newData.produced_roll,
      scheduled_roll: newData.total_scheduled_roll,
      un_schedule_roll: newData.total_un_schedule_roll,
    }

    // 只更新有值的字段
    Object.keys(mappedData).forEach((key) => {
      if (mappedData[key as keyof typeof mappedData] !== undefined && mappedData[key as keyof typeof mappedData] !== null)
        (formData as any)[key] = mappedData[key as keyof typeof mappedData]
    })

    // 处理原料信息
    if (newData.material_ratios && Array.isArray(newData.material_ratios)) {
      materialList.value = newData.material_ratios.map((item: any) => ({
        raw_material_code: item.raw_material_code || '',
        raw_material_name: item.raw_material_name || '',
        raw_material_id: item.raw_material_id || 0,
        weaving_category: item.weaving_category || '',
        weaving_category_id: item.weaving_category_id || 0,
        supplier_name: item.supplier_name || '',
        supplier_id: item.supplier_id || 0,
        brand: item.raw_material_brand || '',
        batch_num: item.raw_material_batch_number || '',
        dyelot_number: item.raw_material_dyelot_number || '',
        color_id: item.raw_material_color_id || 0,
        color_name: item.raw_material_color_name || '',
        yarn_ratio: item.material_ratio || 0,
        raw_material_loss: item.material_loss || 0,
        production_notify_order_detail_id: item.production_notify_order_detail_id || 0,
      }))
    }
  }
}, { immediate: true, deep: true })
function handleChangeValue(value) {
  if (typeof value === 'string') {
    formData.machine_number = value
    formData.machine_number_id = value
    return
  }
  formData.machine_number = value.name
  formData.machine_number_id = value.id
}
</script>

<template>
  <vxe-modal
    v-model="visible"
    show-footer
    :title="dialogTitle"
    width="1200px"
    :mask="false" :lock-view="false" :esc-closable="true"
    resize
    :before-close="handleClose"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="scheduling-form"
    >
      <!-- 第一行：厂区名称、生产单号、机台号、排产条数 -->
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="织厂名称:" prop="unit_id">
            <SelectDialog
              v-model="formData.unit_id"
              api="business_unitlist"
              :query="{ unit_type_id: BusinessUnitIdEnum.knittingFactory, name: componentRemoteSearch.unit_name }"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @change-value="handleUnitChange"
              @change-input="val => (componentRemoteSearch.unit_name = val)"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="生产单号:" prop="production_number">
            <el-input
              v-model="formData.production_number"
              :disabled="!formData.unit_id"
              placeholder="请输入生产单号"
              :readonly="true"
              :style="isEditMode ? 'cursor: not-allowed;' : 'cursor: pointer;'"
              @click="!isEditMode && openProductionNoticeModal()"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="机台号:" prop="machine_number">
            <SelectComponents
              :model-value="formData.machine_number_id"
              allow-create
              :disabled="isEditMode"
              quick-add-link="/systemTools/detail/10023"
              quick-add-premission="Dictionary_add"
              api="GetDictionaryDetailEnumListApi"
              :query="{ dictionary_id: DictionaryType.machineNumber }"
              label-field="name"
              value-field="id"
              clearable
              @change-value="handleChangeValue"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="排产条数:" prop="scheduled_count">
            <el-input-number v-model="formData.scheduled_count" :min="0" controls-position="right" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第二行：纱批、合同序号 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="纱批:" prop="yarn_batch">
            <el-input v-model="formData.yarn_batch" placeholder="请输入纱批" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同序号:" prop="contract_number">
            <el-input
              v-model="formData.contract_number"
              placeholder="请输入合同序号"
              :readonly="isEditMode"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第三行：用料批号、合同备注 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用料批号:" prop="material_batch">
            <el-input v-model="formData.material_batch" placeholder="请输入用料批号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同备注:" prop="contract_remark">
            <el-input v-model="formData.contract_remark" placeholder="生产通知单单备注，支持修改" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第四行：纱牌 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="纱牌:" prop="yarn_brand">
            <el-input v-model="formData.yarn_brand" placeholder="请输入纱牌" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第五行：坯布编号、坯布名称、针寸数、坯布颜色 -->
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="坯布名称:" prop="grey_fabric_name">
            {{ formatHashTag(formData.grey_fabric_code, formData.grey_fabric_name) }}
            <!-- <SelectMergeComponent
              v-model="formData.grey_fabric_id"
              :custom-label="(row:any) => `${formatHashTag(row.code, row.name)}`"
              :multiple="false"
              api-name="GetGreyFabricInfoListUseByOthersMenu"
              remote
              remote-key="code_or_name"
              remote-show-suffix
              placeholder="坯布编号、坯布名称"
              value-field="id"
            /> -->
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="针寸数:" prop="needle_count">
            <el-input v-model="formData.needle_count" placeholder="请输入针寸数" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="坯布颜色:" prop="grey_fabric_color">
            {{ formData.grey_fabric_color }}
            <!-- <SelectComponents v-model="formData.gray_fabric_color_id" api="getInfoProductGrayFabricColorList" label-field="name" value-field="id" clearable /> -->
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第六行：客户名称、款号、纱名 -->
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="客户名称:" prop="customer_name">
            <SelectCustomerDialog
              v-model="formData.customer_id"
              is-merge
              field="name"
              :default-value="{
                id: formData.customer_id,
                name: formData.customer_name,
                code: formData.customer_code,
              }"
              show-choice-system
              @change-value="customerChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="款号:" prop="style_no">
            <el-input v-model="formData.style_no" placeholder="请输入款号" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="纱名:" prop="yarn_name">
            <el-input v-model="formData.yarn_name" placeholder="请输入纱名" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第七行：生产条数、已排条数、未排条数 -->
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="生产条数:">
            {{ formatRollDiv(formData.produced_roll) }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="已排条数:">
            {{ formatRollDiv(formData.scheduled_roll) }}
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="未排条数:">
            {{ formatRollDiv(formData.un_schedule_roll) }}
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 原料信息表格 -->
      <div class="material-section">
        <h4>原料信息</h4>
        <Table
          :config="tableConfig"
          :table-list="materialList"
          :column-list="columnList"
        >
          <template #weaving_category="{ row }">
            <vxe-input
              v-model="row.weaving_category"
              size="mini"
              maxlength="200"
            />
          </template>
          <template #supplier_name="{ row }">
            <SelectDialog
              v-model="row.supplier_id"
              api="BusinessUnitSupplierEnumlist"
              :query="{ name: row.supplier_name }"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              @on-input="val => (row.supplier_name = val)"
            />
          </template>
          <template #brand="{ row }">
            <vxe-input v-model="row.brand" placeholder="请输入原料品牌" />
          </template>
          <template #batch_num="{ row }">
            <vxe-input v-model="row.batch_num" placeholder="请输入原料批号" />
          </template>
          <template #dyelot_number="{ row }">
            <vxe-input v-model="row.dyelot_number" placeholder="请输入原料缸号" />
          </template>
          <template #color_id="{ row }">
            <SelectDialog
              v-model="row.color_id"
              api="GetRawMaterialColor"
              toast-msg=""
              label-field="name"
              :query="{
                raw_matl_id: row.src_raw_matl_id,
                name: row.color_name,
              }"
              :column-list="[
                {
                  field: 'name',
                  title: '名称',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'name',
                      isEdit: true,
                      title: '名称',
                      minWidth: 100,
                    },
                  ],
                },
                {
                  field: 'code',
                  title: '编号',
                  minWidth: 100,
                  isEdit: true,
                  colGroupHeader: true,
                  childrenList: [
                    {
                      field: 'code',
                      isEdit: true,
                      title: '编号',
                      minWidth: 100,
                    },
                  ],
                },
              ]"
              :table-column="[
                {
                  field: 'name',
                  title: '原料颜色',
                  minWidth: 100,
                },
              ]"
              @on-input="(val) => (row.color_name = val)"
              @change-value="(item:any) => changeColor(item, row)"
            />
          </template>
        </Table>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">
          取消
        </el-button>
        <el-button type="primary" @click="handleSaveAndExit">
          保存并退出
        </el-button>
      </div>
    </template>
  </vxe-modal>

  <!-- 选择生产通知单弹窗 -->
  <SelectProductionNotice
    ref="productionNoticeModalRef"
    v-model="productionNoticeModalVisible"
    title="选择生产通知单"
    :order_no="formData.production_number"
    @submit="handleProductionNoticeSubmit"
  />
</template>

<style lang="scss" scoped>
.scheduling-form {
  .material-section {
    margin-top: 30px;

    h4 {
      margin-bottom: 15px;
      color: #333;
      font-weight: 600;
    }
  }

  // 纱批输入框背景色
  .yarn-batch-input {
    ::v-deep .el-input__inner {
      background-color: #e8f5e8;
    }
  }

  // 坯布编号输入框背景色
  .grey-fabric-input {
    ::v-deep .el-input__inner {
      background-color: #e8f5e8;
    }
  }

  // 客户名称输入框背景色
  .customer-input {
    ::v-deep .el-input__inner {
      background-color: #e8f5e8;
    }
  }

  // 款号输入框背景色
  .style-input {
    ::v-deep .el-input__inner {
      background-color: #e8f5e8;
    }
  }

  // 合同备注输入框背景色
  .contract-remark-input {
    ::v-deep .el-input__inner {
      background-color: #fff2e8;
    }
  }

  // 警告文字样式
  .warning-text {
    color: #ff4d4f;
    font-size: 12px;
    line-height: 1.2;
  }

  // 信息文字样式
  .info-text {
    color: #ff4d4f;
    font-size: 12px;
    line-height: 1.2;
  }

  // 合同警告区域
  .contract-warning {
    display: flex;
    align-items: center;
    height: 32px;
    padding-left: 10px;
  }

  // 用料批号警告
  .material-warning {
    margin-top: 4px;
  }

  // 纱牌警告
  .yarn-warning {
    margin-top: 4px;
  }

  // 纱名信息
  .yarn-name-info {
    margin-top: 4px;
  }

  // 未排条数信息
  .unscheduled-info {
    margin-top: 4px;
  }
}

.dialog-footer,
.modal-footer {
  text-align: right;
}

::v-deep .el-form-item__label {
  font-weight: 500;
  color: #333;
}

::v-deep .el-input,
::v-deep .el-select {
  width: 100%;
}

::v-deep .el-row {
  margin-bottom: 20px;
}

::v-deep .el-form-item {
  margin-bottom: 0;
}
</style>
