<script setup lang="ts">
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import MachineSchedulingDialog from './MachineSchedulingDialog.vue'
import DescriptionsFormItem from '@/components/DescriptionsFormItem.vue'
import FildCard from '@/components/FildCard.vue'
import SelectComponents from '@/components/SelectComponents/index.vue'
import Table from '@/components/Table.vue'
import { getWeavingProcess } from '@/api/greyFabricInformation'
import { GetFineCodeList, GetProductionScheduleOrderList } from '@/api/scheduleProduction'

// Props 定义
interface Props {
  mode?: 'add' | 'edit' | 'detail'
  productionNotifyOrderId?: number // 生产通知单ID
  externalData?: any // 外部数据（包含织厂和生产通知单信息）
  modelValue?: {
    weavingForm: any
    shuttleWeavingData: any
    fabricFlyData: any
  }
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'add',
  productionNotifyOrderId: undefined,
  modelValue: () => ({
    weavingForm: {
      weaving_specifications_id: [],
      weaving_specifications_name_list: '',
      needle_size: '',
      total_needle_size: '',
      weaving_loss: '',
      loom_brand: '',
      loom_model_id: '',
      loom_model_name: '',
      machines_num: '',
      upper_needle: '',
      yarn_batch: '',
      lower_needle: '',
      yarn_arrange: '',
      yarn_length: '',
      packaging_require: '',
    },
    shuttleWeavingData: {
      warp_density: '',
      weft_density: '',
      reed_inner_width: '',
      reed_outer_width: '',
      reed_no: '',
      penetration_number: '',
      upper_weft_density: '',
      lower_weft_density: '',
      gf_theory_gram_width: '',
      total_warp_pieces: '',
      tableData: [],
      warp_arrangement: '',
      tableData1: [],
      weft_arrangement: '',
    },
    fabricFlyData: {
      tableData1: [],
      tableData2: [],
    },
  }),
})
// Emits 定义
const emit = defineEmits<{
  'update:modelValue': [value: any]
  'loomModelSelect': [item: any]
}>()

const showUnprint = ref(false)

// 响应式数据
const activeName = ref('first')
const weavingForm = reactive({
  weaving_specifications_id: [],
  weaving_specifications_name_list: '',
  needle_size: '',
  total_needle_size: '',
  weaving_loss: '',
  loom_brand: '',
  loom_model_id: '',
  loom_model_name: '',
  machines_num: '',
  upper_needle: '',
  yarn_batch: '',
  lower_needle: '',
  yarn_arrange: '',
  yarn_length: '',
  packaging_require: '',
  ...props.modelValue.weavingForm,
})
const shuttleWeavingData = reactive({ ...props.modelValue.shuttleWeavingData })
const fabricFlyData = reactive({ ...props.modelValue.fabricFlyData })

// 计算属性：是否为只读模式
const isReadonly = computed(() => props.mode === 'detail')

// 监听 props 变化
watch(() => props.modelValue, (newValue) => {
  if (newValue.weavingForm)
    Object.assign(weavingForm, newValue.weavingForm)

  if (newValue.shuttleWeavingData)
    Object.assign(shuttleWeavingData, newValue.shuttleWeavingData)

  if (newValue.fabricFlyData)
    Object.assign(fabricFlyData, newValue.fabricFlyData)
}, { deep: true })

// 左边表格API - 生产排产单列表
const {
  fetchData: fetchProductionScheduleData,
  data: productionScheduleData,
  loading: productionScheduleLoading,
  success: productionScheduleSuccess,
  msg: productionScheduleMsg,
} = GetProductionScheduleOrderList()

// 右边表格API - 细码列表
const {
  fetchData: fetchFineCodeData,
  data: fineCodeData,
  loading: fineCodeLoading,
  success: fineCodeSuccess,
  msg: fineCodeMsg,
} = GetFineCodeList()
// 监听tab切换，只有在布飞打印tab时才请求数据
watch(() => activeName.value, (newTab) => {
  console.log('Tab切换:', newTab)
  if (newTab === 'third' && props.productionNotifyOrderId) {
    console.log('切换到布飞打印tab，开始获取生产排产单列表...')
    getProductionScheduleList()
  }
})

// 监听生产通知单ID变化，如果当前在布飞打印tab则请求数据
watch(() => props.productionNotifyOrderId, (newId, oldId) => {
  console.log('生产通知单ID变化:', { newId, oldId })
  if (newId && activeName.value === 'third') {
    console.log('生产通知单ID变化且在布飞打印tab，开始获取生产排产单列表...')
    getProductionScheduleList()
  }
})

// 监听显示未打印条码变化，重新获取右边表格数据
watch(() => showUnprint.value, () => {
  // 如果有选中的生产排产单，重新获取细码列表
  const selectedRow = fabricFlyData.tableData1.find((item: any) => item.selected)
  if (selectedRow && selectedRow.id)
    getFineCodeList(selectedRow.id)
})

// 机台排产弹窗
const showMachineSchedulingDialog = ref(false)
const machineSchedulingDialogData = ref<any>({})

// 排纱文本按钮
const btnList = ref<any>([])
const {
  fetchData: ApiWeavingList,
  data: datalist,
  success: WeavingSuccess,
  msg: WeavingMsg,
} = getWeavingProcess()

async function getBtnData() {
  await ApiWeavingList()
  if (WeavingSuccess.value)
    btnList.value = datalist.value?.list || []
  else
    ElMessage.error(WeavingMsg.value)
}

// 获取左边表格数据 - 根据生产通知单ID获取生产排产单列表
async function getProductionScheduleList() {
  if (!props.productionNotifyOrderId) {
    console.warn('生产通知单ID为空，无法获取生产排产单列表')
    return
  }

  try {
    await fetchProductionScheduleData({
      production_notify_order_id: props.productionNotifyOrderId,
    })
    if (!productionScheduleSuccess.value)
      ElMessage.error(productionScheduleMsg.value)
    // 更新左边表格数据
    fabricFlyData.tableData1 = productionScheduleData.value?.list || []

    if (fabricFlyData.tableData1.length > 0)
      handleProductionScheduleRowClick(fabricFlyData.tableData1[0])
  }
  catch (error) {
    console.error('获取生产排产单列表失败:', error)
    ElMessage.error('获取机号排产数据失败')
  }
}

// 获取右边表格数据 - 根据生产排产单ID获取细码列表
async function getFineCodeList(productionScheduleOrderId: number) {
  if (!productionScheduleOrderId) {
    console.warn('生产排产单ID为空，无法获取细码列表')
    return
  }

  try {
    const params: any = {
      production_schedule_order_id: productionScheduleOrderId,
    }

    // 如果勾选了"显示未打印条码"，则只获取未打印的数据
    if (showUnprint.value)
      params.print_status = 1 // 1表示未打印

    await fetchFineCodeData(params)
    if (!fineCodeSuccess.value)
      ElMessage.error(fineCodeMsg.value)
    // 更新右边表格数据
    fabricFlyData.tableData2 = fineCodeData.value?.list || []
    console.log('细码列表数据:', fineCodeData.value)
  }
  catch (error) {
    console.error('获取细码列表失败:', error)
    ElMessage.error('获取布飞打印数据失败')
  }
}

// 处理左边表格行点击 - 获取对应的细码列表
function handleProductionScheduleRowClick(params: any) {
  console.log('左边表格行点击事件触发:', params)
  // 处理不同的事件参数格式
  const row = params.row || params
  console.log('解析后的行数据:', row)
  if (row && row.id) {
    console.log('开始获取细码列表，排产单ID:', row.id)
    getFineCodeList(row.id)
  }
  else {
    console.warn('行数据无效或缺少ID:', row)
  }
}

// 处理左边表格选择变化 - 确保只能单选
function handleProductionScheduleSelection(params: any) {
  console.log('表格选择变化:', params)
  const { checked, row } = params

  if (checked) {
    // 如果选中当前行，先取消所有行的选择，然后只选中当前行
    fabricFlyData.tableData1.forEach((item: any) => {
      item.selected = false
    })
    // 设置当前行为选中状态
    row.selected = true
  }
  // 如果是取消选中，row.selected会自动变为false，不需要额外处理

  // 触发响应式更新
  updateModelValue()
}

// 表格配置
const tableConfig = computed(() => ({
  showPagition: false,
  showSlotNums: true,
  showCheckBox: false,
  showOperate: !isReadonly.value,
  height: 300,
  operateWidth: '100',
  showSort: false,
}))
const tableConfig1 = computed(() => ({
  showPagition: false,
  showSlotNums: true,
  showCheckBox: true,
  showRadio: false, // 确保不显示单选按钮
  showOperate: false,
  height: 300,
  showSort: false,
  loading: productionScheduleLoading.value,
  cellClick: handleProductionScheduleRowClick, // 添加行点击事件
  checkboxConfig: {
    checkField: 'selected',
    highlight: true,
    reserve: false,
    showHeader: false, // 不显示全选按钮
    checkMethod: ({ row }: any) => {
      // 如果当前行已选中，允许取消勾选
      if (row.selected)
        return true

      // 如果当前行未选中，允许选中（在handleSelectionChange中处理取消其他行）
      return true
    },
  },
  handleSelectionChange: handleProductionScheduleSelection,
}))
const tableConfig2 = computed(() => ({
  showPagition: false,
  showSlotNums: true,
  showCheckBox: !isReadonly.value,
  showOperate: true,
  height: 300,
  operateWidth: '100',
  showSort: false,
  loading: fineCodeLoading.value,
}))
const columnList1 = ref([
  {
    field: 'remark',
    title: '经纱组合',
    soltName: 'remark',
  },
  {
    field: 'total_use_weight',
    title: '总用量',
    soltName: 'total_use_weight',
  },
  {
    field: 'net_use_weight',
    title: '净用量',
    soltName: 'net_use_weight',
  },
])

const columnList2 = ref([
  {
    field: 'remark',
    title: '纬纱组合',
    soltName: 'remark',
  },
  {
    field: 'total_use_weight',
    title: '总用量',
    soltName: 'total_use_weight',
  },
  {
    field: 'net_use_weight',
    title: '净用量',
    soltName: 'net_use_weight',
  },
])
const columnList3 = ref([
  {
    field: 'machine_number',
    title: '机台号',
    width: '100',
  },
  {
    field: 'order_no',
    title: '布飞数',
    width: '100',
  },
  {
    field: 'in_warehouse_weight',
    title: '进仓数量',
    width: '100',
    isWeight: true,
  },
  {
    field: 'in_warehouse_roll',
    title: '进仓条数',
    width: '100',
    isPrice: true,
  },
  {
    field: 'weighing_weight',
    title: '称重数量',
    width: '100',
    isWeight: true,
  },
  {
    field: 'weighing_roll',
    title: '称重条数',
    width: '100',
    isPrice: true,
  },
  {
    field: 'inspection_weight',
    title: '验布数量',
    width: '100',
    isWeight: true,
  },
  {
    field: 'inspection_roll',
    title: '验布条数',
    width: '100',
    isPrice: true,
  },
  {
    field: 'out_warehouse_weight',
    title: '出仓数量',
    width: '100',
    isWeight: true,
  },
  {
    field: 'out_warehouse_roll',
    title: '出仓条数',
    width: '100',
    isPrice: true,
  },
  {
    field: 'stock_weight',
    title: '库存数量',
    width: '100',
    isWeight: true,
  },
  {
    field: 'stock_roll',
    title: '库存条数',
    width: '100',
    isPrice: true,
  },
])

const columnList4 = ref([
  {
    field: 'machine_number',
    title: '机台号',
    width: '100',
  },
  {
    field: 'fabric_piece_code',
    title: '条形码',
    width: '100',
  },
  {
    field: 'volume_number',
    title: '卷号',
    width: '100',
  },
  {
    field: 'grey_fabric_level_id',
    title: '坯布等级',
    width: '100',
    soltName: 'grey_fabric_level_name',
  },
  {
    field: 'quality_remark',
    title: '质量备注',
    width: '100',
  },
  {
    field: 'production_schedule_order_id',
    title: '纱批',
    width: '100',
    soltName: 'yarn_batch',
  },
  {
    field: 'production_schedule_order_id',
    title: '原料批号',
    width: '100',
    soltName: 'material_batch_numbers',
  },
  {
    field: 'production_schedule_order_id',
    title: '纱牌',
    width: '100',
    soltName: 'yarn_brand',
  },
  {
    field: 'in_warehouse_weight',
    title: '进仓数量',
    width: '100',
    isWeight: true,
  },
  {
    field: 'in_warehouse_roll',
    title: '进仓条数',
    width: '100',
    isPrice: true,
  },
  {
    field: 'weighing_weight',
    title: '称重数量',
    width: '100',
    isWeight: true,
  },
  {
    field: 'weighing_roll',
    title: '称重条数',
    width: '100',
    isPrice: true,
  },
  {
    field: 'inspection_weight',
    title: '验布数量',
    width: '100',
    isWeight: true,
  },
  {
    field: 'inspection_roll',
    title: '验布条数',
    width: '100',
    isPrice: true,
  },
  {
    field: 'out_warehouse_weight',
    title: '出仓数量',
    width: '100',
    isWeight: true,
  },
  {
    field: 'out_warehouse_roll',
    title: '出仓条数',
    width: '100',
    isPrice: true,
  },
  {
    field: 'stock_weight',
    title: '库存数量',
    width: '100',
    isWeight: true,
  },
  {
    field: 'stock_roll',
    title: '库存条数',
    width: '100',
    isPrice: true,
  },
  {
    field: 'create_time',
    title: '查布时间',
    width: '100',
  },
  {
    field: 'printer_name',
    title: '打印人',
    width: '100',
  },
  {
    field: 'print_time',
    title: '打印时间',
    width: '100',
  },
])
// 方法定义
function validateNumber(value: string, field: string) {
  // 只允许数字输入
  const numericValue = value.replace(/[^\d]/g, '')
  weavingForm[field] = numericValue
  updateModelValue()
}

function handleYarnArrangeClick(name: string) {
  weavingForm.yarn_arrange += name
  updateModelValue()
}

function handleLoomModelSelect(item: any) {
  emit('loomModelSelect', item)
}

function handleTableAdd(type: number) {
  if (props.mode === 'edit') {
    // 编辑模式下的处理逻辑
    if (type === 1) {
      nextTick(() => {
        // 这里需要通过 ref 获取表格实例，但由于组件封装，暂时使用简单的添加方式
        shuttleWeavingData.tableData.push({})
      })
    }
    else if (type === 2) {
      nextTick(() => {
        shuttleWeavingData.tableData1.push({})
      })
    }
  }
  else {
    // 新增模式下的处理逻辑
    if (type === 1)
      shuttleWeavingData.tableData.push({})
    else if (type === 2)
      shuttleWeavingData.tableData1.push({})
  }
  updateModelValue()
}

function handleTableDelete(type: number, rowIndex: number) {
  if (type === 1) {
    shuttleWeavingData.tableData = shuttleWeavingData.tableData.filter(
      (item: any, index: number) => rowIndex !== index,
    )
  }
  else if (type === 2) {
    shuttleWeavingData.tableData1 = shuttleWeavingData.tableData1.filter(
      (item: any, index: number) => index !== rowIndex,
    )
  }
  updateModelValue()
}

function updateModelValue() {
  emit('update:modelValue', {
    weavingForm: { ...weavingForm },
    shuttleWeavingData: { ...shuttleWeavingData },
    fabricFlyData: { ...fabricFlyData },
  })
}

function handlePrint() {
  // 立即打印逻辑

  // 这里可以调用打印API
}

// 暴露方法给父组件
defineExpose({
  setData: (data: any) => {
    // 设置织造工艺数据
    if (data.weavingForm)
      Object.assign(weavingForm, data.weavingForm)

    // 设置梭织工艺数据
    if (data.shuttleWeavingData)
      Object.assign(shuttleWeavingData, data.shuttleWeavingData)

    // 设置布飞打印数据
    if (data.fabricFlyData)
      Object.assign(fabricFlyData, data.fabricFlyData)
  },
  getData: () => ({
    weavingForm: { ...weavingForm },
    shuttleWeavingData: { ...shuttleWeavingData },
    fabricFlyData: { ...fabricFlyData },
  }),
  // 兼容原有的方法名
  setForm: (form: any) => {
    Object.assign(weavingForm, form)
  },
  getForm: () => ({ ...weavingForm }),
  setCustomState: (customState: any) => {
    Object.assign(shuttleWeavingData, customState)
  },
  getCustomState: () => ({ ...shuttleWeavingData }),
})
function handlePrintAll() {

}
function handleProduce() {
  if (isReadonly.value)
    return

  // 获取选中的生产排产单数据（如果有的话）
  const selectedRow = fabricFlyData.tableData1.find((item: any) => item.selected)

  // 使用外部数据或选中行数据
  const rowData = selectedRow || {}

  handleProduceForRow(rowData)
}

// 为指定行打开机台排产弹框
function handleProduceForRow(row: any) {
  if (isReadonly.value)
    return

  // 准备传递给弹框的数据
  const dialogData = {
    // 传递外部数据
    ...props.externalData,
    // 织厂信息 - 优先使用外部数据，其次使用行数据
    weave_factory_id: row.weave_factory_id || props.externalData?.weaving_mill_id,
    weave_factory_name: row.weave_factory_name || props.externalData?.weaving_mill_name,

    // 生产通知单信息 - 优先使用外部数据
    production_notify_order_id: props.productionNotifyOrderId,
    production_notify_order_no: row.production_notify_order_no || props.externalData?.order_no,

    // 机台号 - 如果有选中行则使用行数据，否则为空
    machine_number: row.machine_number || '',
    machine_number_id: row.machine_number_id || '',

    // 其他相关信息 - 优先使用外部数据
    grey_fabric_id: props.externalData?.grey_fabric_id || row.grey_fabric_id,
    grey_fabric_name: props.externalData?.grey_fabric_name || row.grey_fabric_name,
    customer_id: props.externalData?.customer_id || row.customer_id,
    customer_name: props.externalData?.customer_name || row.customer_name,

    // 传递完整的选中行数据（如果有的话）
    selectedProductionScheduleOrder: row,
  }

  console.log('机台排产弹框数据:', dialogData)

  // 将数据传递给弹框组件
  machineSchedulingDialogData.value = dialogData
  showMachineSchedulingDialog.value = true
}

// 机台排产保存
function handleMachineSchedulingSave(data: any) {
  console.log('保存机台排产数据:', data)
  // 这里可以调用API保存数据
  ElMessage.success('机台排产保存成功')
}

// 机台排产保存并继续
function handleMachineSchedulingSaveAndContinue(data: any) {
  console.log('保存并继续机台排产数据:', data)
  // 这里可以调用API保存数据
  ElMessage.success('机台排产保存成功，可继续添加')
}
onMounted(() => {
  getBtnData()
})
</script>

<template>
  <!-- 工艺要求 -->
  <FildCard title="" class="mt-[5px]" :tool-bar="false">
    <el-tabs v-model="activeName" class="demo-tabs">
      <el-tab-pane label="织造工艺" name="first">
        <el-form
          :inline="true"
          :label-width="100"
          :model="weavingForm"
          class="demo-form-inline"
        >
          <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
            <DescriptionsFormItem label="织造规格:">
              <template #content>
                <el-form-item v-if="!isReadonly">
                  <SelectComponents
                    v-model="weavingForm.weaving_specifications_id"
                    multiple
                    api="getInfoProductWeaveSpecificationList"
                    label-field="name"
                    value-field="id"
                    clearable
                  />
                </el-form-item>
                <span v-else>{{ weavingForm.weaving_specifications_name_list || '-' }}</span>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="针寸数:">
              <template #content>
                <el-form-item v-if="!isReadonly">
                  <vxe-input
                    v-model="weavingForm.needle_size"
                    type="text"
                    @input="validateNumber($event, 'needle_size')"
                  />
                </el-form-item>
                <span v-else>{{ weavingForm.needle_size || '-' }}</span>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="总针数:">
              <template #content>
                <el-form-item v-if="!isReadonly">
                  <vxe-input
                    v-model="weavingForm.total_needle_size"
                    type="text"
                    @input="validateNumber($event, 'total_needle_size')"
                  />
                </el-form-item>
                <span v-else>{{ weavingForm.total_needle_size || '-' }}</span>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="织造损耗:">
              <template #content>
                <el-form-item v-if="!isReadonly">
                  <vxe-input v-model="weavingForm.weaving_loss" type="float">
                    <template #suffix>
                      %
                    </template>
                  </vxe-input>
                </el-form-item>
                <span v-else>{{ weavingForm.weaving_loss ? `${weavingForm.weaving_loss}%` : '-' }}</span>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="织机品牌:">
              <template #content>
                <el-form-item v-if="!isReadonly">
                  <el-input v-model="weavingForm.loom_brand" />
                </el-form-item>
                <span v-else>{{ weavingForm.loom_brand || '-' }}</span>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="织机机型:">
              <template #content>
                <el-form-item v-if="!isReadonly">
                  <SelectComponents
                    v-model="weavingForm.loom_model_id"
                    api="getInfoProductLoomModelList"
                    label-field="name"
                    value-field="id"
                    clearable
                    @select="handleLoomModelSelect"
                  />
                </el-form-item>
                <span v-else>{{ weavingForm.loom_model_name || '-' }}</span>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="安排机台数:">
              <template #content>
                <el-form-item v-if="!isReadonly">
                  <vxe-input
                    v-model="weavingForm.machines_num"
                    type="integer"
                    min="0"
                  />
                </el-form-item>
                <span v-else>{{ weavingForm.machines_num || '-' }}</span>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="上针:" :copies="2">
              <template #content>
                <el-form-item v-if="!isReadonly">
                  <vxe-input
                    v-model="weavingForm.upper_needle"
                    type="text"
                    style="width: 100%"
                  />
                </el-form-item>
                <span v-else>{{ weavingForm.upper_needle || '-' }}</span>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="纱批:" :copies="2">
              <template #content>
                <el-form-item v-if="!isReadonly">
                  <vxe-input
                    v-model="weavingForm.yarn_batch"
                    style="width: 100%"
                    type="text"
                  />
                </el-form-item>
                <span v-else>{{ weavingForm.yarn_batch || '-' }}</span>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="下针:" :copies="2">
              <template #content>
                <el-form-item v-if="!isReadonly">
                  <vxe-input
                    v-model="weavingForm.lower_needle"
                    style="width: 100%"
                  />
                </el-form-item>
                <span v-else>{{ weavingForm.lower_needle || '-' }}</span>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="排批:" :copies="2">
              <template #content>
                <el-form-item v-if="!isReadonly">
                  <vxe-textarea
                    v-model="weavingForm.yarn_arrange"
                    style="width: 100%"
                    :autosize="{ minRows: 3, maxRows: 4 }"
                  />
                  <div class="button_text_content">
                    <div
                      v-for="item in btnList"
                      :key="item.id"
                      class="custom_button"
                      @click="handleYarnArrangeClick(item.name)"
                    >
                      {{ item.name }}
                    </div>
                  </div>
                </el-form-item>
                <span v-else style="white-space: pre-wrap;">{{ weavingForm.yarn_arrange || '-' }}</span>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="纱长:" :copies="2">
              <template #content>
                <el-form-item v-if="!isReadonly">
                  <vxe-input
                    v-model="weavingForm.yarn_length"
                    style="width: 100%"
                  />
                </el-form-item>
                <span v-else>{{ weavingForm.yarn_length || '-' }}</span>
              </template>
            </DescriptionsFormItem>
            <DescriptionsFormItem label="包装要求:" :copies="2">
              <template #content>
                <el-form-item v-if="!isReadonly">
                  <vxe-textarea
                    v-model="weavingForm.packaging_require"
                    maxlength="200"
                    show-word-count
                  />
                </el-form-item>
                <span v-else style="white-space: pre-wrap;">{{ weavingForm.packaging_require || '-' }}</span>
              </template>
            </DescriptionsFormItem>
          </div>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="梭织工艺" name="second">
        <div class="descriptions_row" :style="{ '--minLabelWidth': '74px' }">
          <DescriptionsFormItem label="经密:">
            <template #content>
              <vxe-input
                v-if="!isReadonly"
                v-model="shuttleWeavingData.warp_density"
                type="float"
                clearable
              >
                <template #suffix>
                  根/cm
                </template>
              </vxe-input>
              <span v-else>{{ shuttleWeavingData.warp_density ? `${shuttleWeavingData.warp_density}根/cm` : '-' }}</span>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="纬密:">
            <template #content>
              <vxe-input
                v-if="!isReadonly"
                v-model="shuttleWeavingData.weft_density"
                type="float"
                clearable
              >
                <template #suffix>
                  根/cm
                </template>
              </vxe-input>
              <span v-else>{{ shuttleWeavingData.weft_density ? `${shuttleWeavingData.weft_density}根/cm` : '-' }}</span>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="钢筘内幅:">
            <template #content>
              <vxe-input
                v-if="!isReadonly"
                v-model="shuttleWeavingData.reed_inner_width"
                type="float"
                clearable
              >
                <template #suffix>
                  cm
                </template>
              </vxe-input>
              <span v-else>{{ shuttleWeavingData.reed_inner_width ? `${shuttleWeavingData.reed_inner_width}cm` : '-' }}</span>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="钢筘边幅:">
            <template #content>
              <vxe-input
                v-if="!isReadonly"
                v-model="shuttleWeavingData.reed_outer_width"
                type="float"
                clearable
              >
                <template #suffix>
                  cm
                </template>
              </vxe-input>
              <span v-else>{{ shuttleWeavingData.reed_outer_width ? `${shuttleWeavingData.reed_outer_width}cm` : '-' }}</span>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="筘号:">
            <template #content>
              <vxe-input v-if="!isReadonly" v-model="shuttleWeavingData.reed_no" type="float" clearable>
                <template #suffix>
                  齿/cm
                </template>
              </vxe-input>
              <span v-else>{{ shuttleWeavingData.reed_no ? `${shuttleWeavingData.reed_no}齿/cm` : '-' }}</span>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="穿入数:">
            <template #content>
              <vxe-input
                v-if="!isReadonly"
                v-model="shuttleWeavingData.penetration_number"
                type="float"
                clearable
              >
                <template #suffix>
                  穿
                </template>
              </vxe-input>
              <span v-else>{{ shuttleWeavingData.penetration_number ? `${shuttleWeavingData.penetration_number}穿` : '-' }}</span>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="上机纬密:">
            <template #content>
              <vxe-input
                v-if="!isReadonly"
                v-model="shuttleWeavingData.upper_weft_density"
                type="float"
                clearable
              >
                <template #suffix>
                  牙
                </template>
              </vxe-input>
              <span v-else>{{ shuttleWeavingData.upper_weft_density ? `${shuttleWeavingData.upper_weft_density}牙` : '-' }}</span>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="下机纬密:">
            <template #content>
              <vxe-input
                v-if="!isReadonly"
                v-model="shuttleWeavingData.lower_weft_density"
                type="float"
                clearable
              >
                <template #suffix>
                  根/cm
                </template>
              </vxe-input>
              <span v-else>{{ shuttleWeavingData.lower_weft_density ? `${shuttleWeavingData.lower_weft_density}根/cm` : '-' }}</span>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="理论坯布克重:">
            <template #content>
              <vxe-input
                v-if="!isReadonly"
                v-model="shuttleWeavingData.gf_theory_gram_width"
                type="float"
                clearable
              >
                <template #suffix>
                  g/m
                </template>
              </vxe-input>
              <span v-else>{{ shuttleWeavingData.gf_theory_gram_width ? `${shuttleWeavingData.gf_theory_gram_width}g/m` : '-' }}</span>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="总经根数:">
            <template #content>
              <vxe-input
                v-if="!isReadonly"
                v-model="shuttleWeavingData.total_warp_pieces"
                type="float"
                clearable
              >
                <template #suffix>
                  根
                </template>
              </vxe-input>
              <span v-else>{{ shuttleWeavingData.total_warp_pieces ? `${shuttleWeavingData.total_warp_pieces}根` : '-' }}</span>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="经纱排列:" copies="2">
            <template #content>
              <el-input
                v-if="!isReadonly"
                v-model="shuttleWeavingData.warp_arrangement"
                type="textarea"
                clearable
              />
              <span v-else style="white-space: pre-wrap;">{{ shuttleWeavingData.warp_arrangement || '-' }}</span>
            </template>
          </DescriptionsFormItem>
          <DescriptionsFormItem label="纬纱排列:" copies="2">
            <template #content>
              <el-input
                v-if="!isReadonly"
                v-model="shuttleWeavingData.weft_arrangement"
                type="textarea"
                clearable
              />
              <span v-else style="white-space: pre-wrap;">{{ shuttleWeavingData.weft_arrangement || '-' }}</span>
            </template>
          </DescriptionsFormItem>
        </div>
        <el-button
          v-if="!isReadonly"
          type="primary"
          style="margin-bottom: 10px"
          @click="handleTableAdd(1)"
        >
          新增
        </el-button>
        <Table
          :ref="mode === 'edit' ? 'tablesRef1' : undefined"
          :config="tableConfig"
          :table-list="shuttleWeavingData.tableData"
          :column-list="columnList1"
        >
          <template #remark="{ row }">
            <el-input v-if="!isReadonly" v-model="row.remark" type="text" clearable />
            <span v-else>{{ row.remark || '-' }}</span>
          </template>
          <template #total_use_weight="{ row }">
            <vxe-input v-if="!isReadonly" v-model="row.total_use_weight" type="float" clearable>
              <template #suffix>
                g/m
              </template>
            </vxe-input>
            <span v-else>{{ row.total_use_weight ? `${row.total_use_weight}g/m` : '-' }}</span>
          </template>
          <template #net_use_weight="{ row }">
            <vxe-input v-if="!isReadonly" v-model="row.net_use_weight" type="float" clearable>
              <template #suffix>
                g/m
              </template>
            </vxe-input>
            <span v-else>{{ row.net_use_weight ? `${row.net_use_weight}g/m` : '-' }}</span>
          </template>
          <template #operate="{ rowIndex }">
            <el-button
              type="danger"
              link
              @click="handleTableDelete(1, rowIndex)"
            >
              删除
            </el-button>
          </template>
        </Table>
        <el-button v-if="!isReadonly" type="primary" style="margin: 10px 0" @click="handleTableAdd(2)">
          新增
        </el-button>
        <Table
          :ref="mode === 'edit' ? 'tablesRef2' : undefined"
          :config="tableConfig"
          :table-list="shuttleWeavingData.tableData1"
          :column-list="columnList2"
        >
          <template #remark="{ row }">
            <el-input v-if="!isReadonly" v-model="row.remark" type="text" clearable />
            <span v-else>{{ row.remark || '-' }}</span>
          </template>
          <template #total_use_weight="{ row }">
            <vxe-input v-if="!isReadonly" v-model="row.total_use_weight" type="float" clearable>
              <template #suffix>
                g/m
              </template>
            </vxe-input>
            <span v-else>{{ row.total_use_weight ? `${row.total_use_weight}g/m` : '-' }}</span>
          </template>
          <template #net_use_weight="{ row }">
            <vxe-input v-if="!isReadonly" v-model="row.net_use_weight" type="float" clearable>
              <template #suffix>
                g/m
              </template>
            </vxe-input>
            <span v-else>{{ row.net_use_weight ? `${row.net_use_weight}g/m` : '-' }}</span>
          </template>
          <template #operate="{ rowIndex }">
            <el-button
              type="danger"
              link
              @click="handleTableDelete(2, rowIndex)"
            >
              删除
            </el-button>
          </template>
        </Table>
      </el-tab-pane>
      <el-tab-pane label="布飞打印" name="third">
        <el-row>
          <el-col :span="12">
            <el-button :disabled="isReadonly" type="primary" plain @click="handleProduce">
              机号排产
            </el-button>
            <div class="flex h-full mt-2">
              <Table
                :ref="mode === 'edit' ? 'tablesRef3' : undefined"
                :config="tableConfig1"
                :table-list="fabricFlyData.tableData1"
                :column-list="columnList3"
              />
              <el-divider direction="vertical" style="height: 100%;" />
            </div>
          </el-col>
          <el-col :span="12">
            <el-button type="primary" plain @click="handlePrintAll">
              打印布飞
            </el-button>
            <el-checkbox v-if="!isReadonly" type="primary" class="ml-2" @click="showUnprint = !showUnprint">
              显示未打印条码
            </el-checkbox>
            <Table
              :ref="mode === 'edit' ? 'tablesRef4' : undefined"
              class=" mt-2"
              :config="tableConfig2"
              :table-list="fabricFlyData.tableData2"
              :column-list="columnList4"
            >
              <template #operate>
                <el-link type="primary" :underline="false" @click="handlePrint">
                  打印
                </el-link>
              </template>
            </Table>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>
  </FildCard>

  <!-- 机台排产弹窗 -->
  <MachineSchedulingDialog
    v-model="showMachineSchedulingDialog"
    :production-data="machineSchedulingDialogData"
    @save="handleMachineSchedulingSave"
    @save-and-continue="handleMachineSchedulingSaveAndContinue"
  />
</template>

<style lang="scss" scoped>
::v-deep .el-form-item {
  width: 100%;
}

.button_text_content {
  display: flex;
  margin-top: 4px;

  .custom_button {
    width: 30px;
    height: 20px;
    background: #409eff;
    color: #fff;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 4px;
    cursor: pointer;
    font-size: 12px;

    &:hover {
      background: #66b1ff;
    }
  }
}
</style>
